## 拍卖未开始
- 查看倒计时: 可以正确显示倒计时

## 拍卖开始 round1
- 观察价格: 正确显示 live round price
- 观察 token sold ， fund raised: 可以显示正确数字
- 观察倒计时: 可以正确显示本轮结束倒计时
- 观察 auction progress 数据: 可以正确显示数字
- 填写 usdt - 小于钱包内持有数量: 可以根据 price 正确计算出获得 token 数量
- 接上步，点击 purchase: 弹出 confirm 浮层，正确显示购买级相关信息
- 接上步，点击 confirm: 正确调用钱包签字，完成交易，正确扣除填写的 usdt
- 填写 usdt - 大于钱包内持有数量: 报错提示
- 填写 usdt 换算 token 大于当前轮可售余额: 当前可售 token 全部成交，同时退回用户多余 usdt
- 填写 ton - 小于钱包内持有数量: 可以根据 price 正确计算出获得 token 数量
- 接上步，点击 purchase: 弹出 confirm 浮层，正确显示购买级相关信息
- 接上步，点击 confirm: 正确调用钱包签字，完成交易，正确扣除填写的 ton
- 填写 ton - 大于钱包内持有数量: 报错提示
- 填写  ton 换算 token 大于当前轮可售余额: 当前可售 token 全部成交，同时退回用户多余 ton
## 两轮拍卖中间
- 暂停 一定时间，计算新一轮 price: 暂停 一定时间，计算新一轮 price，计算完成后自动开始下一轮
## 拍卖开始 round2+
- 观察价格: 正确显示 live round price 和 last round price
- 观察 token sold ， fund raised: 可以显示正确数字
- 观察倒计时: 可以正确显示本轮结束倒计时
- 观察 auction progress 数据: 可以正确显示数字
- 填写 usdt - 小于钱包内持有数量: 可以根据 price 正确计算出获得 token 数量
- 接上步，点击 purchase: 弹出 confirm 浮层，正确显示购买级相关信息
- 接上步，点击 confirm: 正确调用钱包签字，完成交易，正确扣除填写的 usdt
- 填写 usdt - 大于钱包内持有数量: 报错提示
- 填写 usdt 换算 token 大于可售余额: 报错提示
- 填写 ton - 小于钱包内持有数量: 可以根据 price 正确计算出获得 token 数量
- 接上步，点击 purchase: 弹出 confirm 浮层，正确显示购买级相关信息
- 接上步，点击 confirm: 正确调用钱包签字，完成交易，正确扣除填写的 ton
- 填写 ton - 大于钱包内持有数量: 报错提示
- 填写 ton 换算 token 大于可售余额: 报错提示
## 拍卖结束
- 拍卖失败: "fund raised 显示红色，
auction progress 低于 soft cap，显示正确数字。
提示 lanuch failed"
- 拍卖成功: "fund raised 显示蓝色，
auction progress 高于 soft cap，显示正确数据。
提示 lanuch 成功"
- 查看auction History: 可正确显示每轮数据