# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an ONION Token Fair Launch implementation on TON Blockchain featuring:
- Tact-based smart contracts with main auction, user purchase child contracts, and jetton token
- Multi-currency support (TON and USDT payments via Jetton standard)  
- Next.js frontend with real-time auction tracking
- Demo server for off-chain calculations and signature verification
- Security validation via Misti static analysis

## Key Commands

### Main Project (onion-launch/)
- **Build contracts**: `bun blueprint build`
- **Run tests**: `bun blueprint test` or `bun run test`
- **Static analysis**: `bun run misti contracts/onion_auction.tact`
- **Deploy scripts**: `bun run blueprint run` (interactive deployment)

### Frontend (onion-launch/frontend/)
- **Development**: `bun run dev` (Next.js dev server)
- **Build**: `bun run build`
- **Lint**: `bun run lint`
- **Install**: `bun install`

### Demo Server (onion-launch/demo-server/)
- **Development**: `npm run dev` (TypeScript with ts-node)
- **Build**: `npm run build`
- **Test**: `npm test`
- **Generate keys**: `npm run generate-keys`
- **Lint**: `npm run lint`

## Architecture

### Smart Contract Structure
- **OnionAuction** (`contracts/onion_auction.tact`): Main auction contract managing lifecycle, pricing, and creating UserPurchase child contracts
- **UserPurchase** (`contracts/user_purchase.tact`): Individual purchase tracking per user with refund functionality
- **JettonMaster/JettonWallet** (`contracts/jetton/`): Standard jetton implementation for ONION token

### Key Design Patterns
- **Child Contract Pattern**: Uses UserPurchase contracts to prevent unbounded data growth in main contract
- **Multi-Currency Support**: Handles both TON and USDT payments through jetton standard
- **Signature Verification**: Off-chain calculation validation using cryptographic signatures
- **English Auction**: Time-based rounds with automatic price increments

### Frontend Architecture
- **Next.js 15** with App Router and TypeScript
- **TON Connect** integration for wallet connectivity
- **Real-time Updates**: Live auction data synchronization every 2 minutes
- **Components**: Modular design with AuctionStats, PurchaseModule, AuctionHistory, UserPurchases

### Demo Server
- **Express.js** server for off-chain calculations
- **Signature generation** for secure purchase validation
- **Token calculation** endpoints for frontend integration

## Development Workflow

1. **Contract Development**: Edit `.tact` files → `npx blueprint build` → `npx blueprint test`
2. **Security Validation**: Run `npx misti contracts/onion_auction.tact` after contract changes
3. **Frontend Development**: Work in `frontend/` directory with `bun run dev`
4. **Testing**: Comprehensive test suite in `tests/` directory covers all auction scenarios

## Important Files
- `tact.config.json`: Tact compiler configuration
- `jest.config.ts`: Test configuration for TON Sandbox environment
- `CONTRACT_DESIGN.md`: Detailed technical design documentation
- `USDT_INTEGRATION.md`: USDT payment implementation guide

## Testing Strategy
- Unit tests for individual contract functions
- Integration tests for complete auction flows
- Multi-currency testing (TON and USDT)
- Signature verification testing
- Error handling and edge case validation