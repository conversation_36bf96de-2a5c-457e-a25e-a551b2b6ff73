import { to<PERSON>ano } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import { NetworkProvider } from '@ton/blueprint';

export async function run(provider: NetworkProvider) {
    // Contract initialization parameters
    const owner = provider.sender().address!;
    const start_time = BigInt(Math.floor(Date.now() / 1000) + 3600); // 1 hour from now
    const end_time = BigInt(Math.floor(Date.now() / 1000) + 86400); // 24 hours from now
    const soft_cap = toNano('1000'); // 1000 TON
    const hard_cap = toNano('5000'); // 5000 TON
    const total_supply = toNano('1000000'); // 1M tokens

    const onionAuction = provider.open(await OnionAuction.fromInit(
        owner,
        start_time,
        end_time,
        soft_cap,
        hard_cap,
        total_supply
    ));

    await onionAuction.send(
        provider.sender(),
        {
            value: toNano('0.05'),
        }, 'Deploy'
    );

    await provider.waitForDeploy(onionAuction.address);

    console.log('OnionAuction deployed at:', onionAuction.address.toString());
    console.log('Owner:', owner.toString());
    console.log('Start time:', start_time.toString());
    console.log('End time:', end_time.toString());
    console.log('Soft cap:', soft_cap.toString());
    console.log('Hard cap:', hard_cap.toString());
    console.log('Total supply:', total_supply.toString());
}
