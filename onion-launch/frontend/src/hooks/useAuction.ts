'use client'

import { useState, useEffect } from 'react'
import { ApiService, formatCurrency, handleApiError } from '@/lib/apiService'

// Auction status constants
export const AUCTION_STATUS = {
  NOT_STARTED: 0,
  ACTIVE: 1,
  PAUSED_BETWEEN_ROUNDS: 2,
  ENDED_SUCCESS: 3,
  ENDED_FAILED: 4
} as const

export type AuctionStatus = typeof AUCTION_STATUS[keyof typeof AUCTION_STATUS]

export interface AuctionData {
  totalRaised: number
  totalTokensSold: number
  auctionStatus: AuctionStatus
  currentRound: number
  currentPrice: number
  lastRoundPrice?: number
  endTime: number
  roundEndTime?: number
  nextRoundStartTime?: number
  softCap: number
  hardCap: number
  totalSupply: number
  startTime: number
  purchaseCount: number
  remainingTokensInRound?: number
}

export function useAuction() {
  const [auctionData, setAuctionData] = useState<AuctionData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    // Fetch auction data from API service
    const fetchAuctionData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Try to get real data from API service
        const state = await ApiService.getAuctionState()

        const auctionData: AuctionData = {
          totalRaised: parseFloat(formatCurrency(state.total_raised)),
          totalTokensSold: parseFloat(formatCurrency(state.total_tokens_sold)),
          auctionStatus: state.auction_status as AuctionStatus,
          currentRound: state.current_round,
          currentPrice: parseFloat(formatCurrency(state.current_price)),
          endTime: Math.floor(Date.now() / 1000) + 86400, // Mock end time
          softCap: 500000, // Mock soft cap
          hardCap: 2000000, // Mock hard cap
          totalSupply: 1000000, // Mock total supply
          startTime: Math.floor(Date.now() / 1000) - 43200, // Mock start time
          purchaseCount: state.purchase_count
        }

        setAuctionData(auctionData)
        setIsOnline(true)
      } catch (apiError) {
        console.warn('API service unavailable, using mock data:', apiError)
        setError(handleApiError(apiError))
        setIsOnline(false)

        // Generate mock data based on current time to simulate different states
        const now = Math.floor(Date.now() / 1000)
        const mockStartTime = now - 3600 // Started 1 hour ago
        const mockEndTime = now + 86400 // Ends in 24 hours

        // Simulate different auction states for demo
        let mockStatus: AuctionStatus = AUCTION_STATUS.ACTIVE
        let mockRound = 12
        let mockPrice = 0.11
        let mockLastRoundPrice: number | undefined = 0.10

        // You can modify this to test different states
        // Change 'active' to: 'not_started', 'paused', 'ended_success', 'ended_failed'
        const testState: string = 'active'

        if (testState === 'not_started') {
          mockStatus = AUCTION_STATUS.NOT_STARTED
          mockRound = 0
          mockPrice = 0.05
          mockLastRoundPrice = undefined
        } else if (testState === 'paused') {
          mockStatus = AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS
          mockRound = 12
          mockPrice = 0.11
          mockLastRoundPrice = 0.10
        } else if (testState === 'ended_success') {
          mockStatus = AUCTION_STATUS.ENDED_SUCCESS
          mockRound = 24
          mockPrice = 0.25
          mockLastRoundPrice = 0.24
        } else if (testState === 'ended_failed') {
          mockStatus = AUCTION_STATUS.ENDED_FAILED
          mockRound = 15
          mockPrice = 0.15
          mockLastRoundPrice = 0.14
        }
        // else: Active state (default)

        const mockData: AuctionData = {
          totalRaised: mockStatus === AUCTION_STATUS.ENDED_FAILED ? 400000 : 1600000,
          totalTokensSold: 673000,
          auctionStatus: mockStatus,
          currentRound: mockRound,
          currentPrice: mockPrice,
          lastRoundPrice: mockLastRoundPrice,
          endTime: mockEndTime,
          roundEndTime: mockStatus === AUCTION_STATUS.ACTIVE ? now + 3600 : undefined, // Round ends in 1 hour
          nextRoundStartTime: mockStatus === AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS ? now + 300 : undefined, // Next round in 5 minutes
          softCap: 500000,
          hardCap: 2000000,
          totalSupply: 1000000,
          startTime: mockStatus === AUCTION_STATUS.NOT_STARTED ? now + 7200 : mockStartTime, // Starts in 2 hours if not started
          purchaseCount: 1250,
          remainingTokensInRound: mockStatus === AUCTION_STATUS.ACTIVE ? 50000 : undefined
        }

        setAuctionData(mockData)
      } finally {
        setIsLoading(false)
      }
    }

    fetchAuctionData()

    // Set up real-time updates every 30 seconds for better UX
    const interval = setInterval(fetchAuctionData, 30000)

    return () => clearInterval(interval)
  }, [])

  return {
    auctionData,
    isLoading,
    error,
    isOnline,
    clearError: () => setError(null)
  }
}