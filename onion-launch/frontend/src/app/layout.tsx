'use client';

// import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { TonConnectUIProvider } from '@tonconnect/ui-react'
import { Header } from '@/components/Header'

const inter = Inter({ subsets: ['latin'] })

// export const metadata: Metadata = {
//   title: 'ONION Token Fair Launch',
//   description: 'Fair launch platform for ONION token with English auction mechanism',
// }

const manifestUrl = 'https://raw.githubusercontent.com/ton-community/tutorials/main/03-client/test/public/tonconnect-manifest.json'

const mf = 'https://static.tbook.vip/ton/manifest.json'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <TonConnectUIProvider manifestUrl={mf}>
          <Header />
          <main className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
            {children}
          </main>
        </TonConnectUIProvider>
      </body>
    </html>
  )
}