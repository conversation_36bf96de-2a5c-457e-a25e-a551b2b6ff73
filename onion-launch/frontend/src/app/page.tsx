'use client'

import { useState, useEffect } from 'react'
import { Clock, CheckCircle, XCircle } from 'lucide-react'
import { AuctionInfo } from '@/components/AuctionInfo'
import { AuctionStats } from '@/components/AuctionStats'
import { PurchaseModule } from '@/components/PurchaseModule'
import { AuctionHistory } from '@/components/AuctionHistory'
import { UserPurchases } from '@/components/UserPurchases'
import { RoundTransition } from '@/components/RoundTransition'
import { AuctionEndedStatus } from '@/components/AuctionEndedStatus'
import { useAuction, AUCTION_STATUS } from '@/hooks/useAuction'

export default function Home() {
  const { auctionData, isLoading } = useAuction()

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-onion-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-onion-500 to-onion-600 rounded-full mb-6">
          <span className="text-3xl font-bold text-white">🧅</span>
        </div>
        <h1 className="text-4xl lg:text-6xl font-bold bg-gradient-to-r from-onion-600 to-onion-700 bg-clip-text text-transparent mb-4">
          ONION Token Fair Launch
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Experience the future of fair token distribution with our English auction mechanism. 
          Equal opportunity for all participants with transparent pricing.
        </p>
      </div>

      {/* Main Grid */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Left Column - Auction Info & Stats */}
        <div className="lg:col-span-2 space-y-8">
          {/* Show different content based on auction status */}
          {auctionData?.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ||
           auctionData?.auctionStatus === AUCTION_STATUS.ENDED_FAILED ? (
            /* Auction Ended - Show end status */
            <AuctionEndedStatus
              auctionStatus={auctionData.auctionStatus}
              totalRaised={auctionData.totalRaised}
              softCap={auctionData.softCap}
              hardCap={auctionData.hardCap}
              totalTokensSold={auctionData.totalTokensSold}
              totalSupply={auctionData.totalSupply}
              currentRound={auctionData.currentRound}
              finalPrice={auctionData.currentPrice}
              startPrice={0.05}
            />
          ) : (
            /* Auction Active/Paused/Not Started - Show normal layout */
            <>
              <AuctionStats data={auctionData || undefined} />

              {/* Show Round Transition during paused state */}
              {auctionData?.auctionStatus === AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS ? (
                <RoundTransition
                  currentRound={auctionData.currentRound}
                  nextRoundStartTime={auctionData.nextRoundStartTime || 0}
                  lastRoundPrice={auctionData.lastRoundPrice}
                  estimatedNextPrice={auctionData.currentPrice * 1.1} // Mock 10% increase
                  isCalculating={true}
                />
              ) : (
                <AuctionInfo data={auctionData || undefined} />
              )}
            </>
          )}

          <AuctionHistory />
        </div>

        {/* Right Column - Purchase & User Info */}
        <div className="space-y-8">
          {/* Show different purchase states based on auction status */}
          {auctionData?.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ||
           auctionData?.auctionStatus === AUCTION_STATUS.ENDED_FAILED ? (
            /* Auction Ended - Show final message */
            <div className="bg-white rounded-2xl card-shadow p-6">
              <div className="text-center py-8">
                <div className={`flex items-center justify-center w-16 h-16 rounded-full mb-4 mx-auto ${
                  auctionData.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ? 'bg-green-100' : 'bg-red-100'
                }`}>
                  {auctionData.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ? (
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  ) : (
                    <XCircle className="w-8 h-8 text-red-600" />
                  )}
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {auctionData.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ?
                    'Auction Completed Successfully!' : 'Auction Has Ended'}
                </h3>
                <p className="text-gray-600 mb-4">
                  {auctionData.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ?
                    'Thank you for participating! Token distribution will begin soon.' :
                    'The auction did not reach its soft cap. Refunds will be processed automatically.'}
                </p>
                <div className={`border rounded-lg p-4 text-sm ${
                  auctionData.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ?
                    'bg-green-50 border-green-200 text-green-700' :
                    'bg-red-50 border-red-200 text-red-700'
                }`}>
                  {auctionData.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ?
                    'Check your wallet for token distribution updates.' :
                    'All funds will be returned to participant wallets within 24 hours.'}
                </div>
              </div>
            </div>
          ) : auctionData?.auctionStatus === AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS ? (
            /* Paused - Show pause message */
            <div className="bg-white rounded-2xl card-shadow p-6">
              <div className="text-center py-8">
                <div className="flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4 mx-auto">
                  <Clock className="w-8 h-8 text-yellow-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Purchases Temporarily Paused</h3>
                <p className="text-gray-600 mb-4">
                  We're calculating the next round price. Purchases will resume automatically when Round {(auctionData?.currentRound || 0) + 1} begins.
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-sm text-yellow-700">
                  Don't worry - you can still purchase tokens once the next round starts!
                </div>
              </div>
            </div>
          ) : (
            /* Active/Not Started - Show purchase module */
            <PurchaseModule />
          )}

          <UserPurchases />
        </div>
      </div>

      {/* Footer */}
      <footer className="mt-16 pt-8 border-t border-gray-200 text-center text-gray-500">
        <p>&copy; 2025 ONION Token. All rights reserved.</p>
        <p className="mt-2 text-sm">
          Built on TON Blockchain with Tact smart contracts
        </p>
      </footer>
    </div>
  )
}