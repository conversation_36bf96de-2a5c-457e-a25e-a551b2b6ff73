'use client'

import { useState } from 'react'
import { X, Al<PERSON><PERSON><PERSON>gle, CheckCircle, Zap, Clock, Calculator } from 'lucide-react'

interface PurchaseDetails {
  amount: string
  currency: 'TON' | 'USDT'
  tokensToReceive: number
  currentPrice: number
  currentRound: number
  totalCostUSD: number
  timestamp: Date
  nonce?: string
  isSignatureVerified?: boolean
}

interface PurchaseConfirmModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => Promise<void>
  purchaseDetails: PurchaseDetails
  isProcessing: boolean
  walletBalance?: {
    ton: number
    usdt: number
  }
  remainingTokensInRound?: number
}

export function PurchaseConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  purchaseDetails,
  isProcessing,
  walletBalance,
  remainingTokensInRound
}: PurchaseConfirmModalProps) {
  const [hasConfirmed, setHasConfirmed] = useState(false)

  if (!isOpen) return null

  const formatCurrency = (amount: number, currency?: string) => {
    if (currency) {
      return `${amount.toLocaleString()} ${currency}`
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  const formatTokens = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount) + ' ONION'
  }

  const currentBalance = walletBalance ? 
    (purchaseDetails.currency === 'TON' ? walletBalance.ton : walletBalance.usdt) : 0
  
  const hasInsufficientBalance = parseFloat(purchaseDetails.amount) > currentBalance
  const exceedsRoundLimit = remainingTokensInRound && 
    purchaseDetails.tokensToReceive > remainingTokensInRound

  const handleConfirm = async () => {
    if (!hasConfirmed) {
      setHasConfirmed(true)
      return
    }
    
    await onConfirm()
    setHasConfirmed(false)
  }

  const handleClose = () => {
    setHasConfirmed(false)
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-onion-100 rounded-lg">
              <CheckCircle className="w-5 h-5 text-onion-600" />
            </div>
            <h2 className="text-xl font-bold text-gray-900">Confirm Purchase</h2>
            {purchaseDetails.isSignatureVerified && (
              <div className="flex items-center space-x-1 text-sm text-blue-600">
                <Zap className="w-4 h-4" />
                <span>Verified</span>
              </div>
            )}
          </div>
          <button
            onClick={handleClose}
            disabled={isProcessing}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Purchase Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <Calculator className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">Purchase Summary</span>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Amount:</span>
                <span className="font-medium">
                  {formatCurrency(parseFloat(purchaseDetails.amount), purchaseDetails.currency)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">USD Value:</span>
                <span className="font-medium">{formatCurrency(purchaseDetails.totalCostUSD)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Price per ONION:</span>
                <span className="font-medium">{formatCurrency(purchaseDetails.currentPrice)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Round:</span>
                <span className="font-medium">{purchaseDetails.currentRound}</span>
              </div>
              <div className="border-t pt-2 flex justify-between">
                <span className="text-gray-900 font-medium">You will receive:</span>
                <span className="font-bold text-onion-600">
                  {formatTokens(purchaseDetails.tokensToReceive)}
                </span>
              </div>
            </div>
          </div>

          {/* Wallet Balance Check */}
          {walletBalance && (
            <div className={`rounded-lg p-4 ${
              hasInsufficientBalance ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                {hasInsufficientBalance ? (
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                ) : (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                )}
                <span className={`text-sm font-medium ${
                  hasInsufficientBalance ? 'text-red-800' : 'text-green-800'
                }`}>
                  Wallet Balance
                </span>
              </div>
              <div className="text-sm">
                <div className="flex justify-between">
                  <span className={hasInsufficientBalance ? 'text-red-700' : 'text-green-700'}>
                    Your {purchaseDetails.currency} balance:
                  </span>
                  <span className={`font-medium ${
                    hasInsufficientBalance ? 'text-red-800' : 'text-green-800'
                  }`}>
                    {formatCurrency(currentBalance, purchaseDetails.currency)}
                  </span>
                </div>
                {hasInsufficientBalance && (
                  <p className="text-red-700 mt-1">
                    Insufficient balance. You need {formatCurrency(
                      parseFloat(purchaseDetails.amount) - currentBalance, 
                      purchaseDetails.currency
                    )} more.
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Round Limit Check */}
          {exceedsRoundLimit && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="w-4 h-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-800">Round Limit Notice</span>
              </div>
              <div className="text-sm text-orange-700">
                <p>
                  Only {formatTokens(remainingTokensInRound!)} tokens available in current round.
                </p>
                <p className="mt-1">
                  You will receive {formatTokens(remainingTokensInRound!)} tokens and excess payment will be refunded.
                </p>
              </div>
            </div>
          )}

          {/* Signature Info */}
          {purchaseDetails.isSignatureVerified && purchaseDetails.nonce && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Zap className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Signature Verification</span>
              </div>
              <div className="text-sm text-blue-700">
                <p>This purchase has been pre-verified with signature authentication.</p>
                <p className="mt-1">
                  Calculated at: {purchaseDetails.timestamp.toLocaleTimeString()}
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  Nonce: {purchaseDetails.nonce}
                </p>
              </div>
            </div>
          )}

          {/* Confirmation Checkbox */}
          {!hasConfirmed && (
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                id="confirm-purchase"
                checked={hasConfirmed}
                onChange={(e) => setHasConfirmed(e.target.checked)}
                className="mt-1 w-4 h-4 text-onion-600 border-gray-300 rounded focus:ring-onion-500"
              />
              <label htmlFor="confirm-purchase" className="text-sm text-gray-700">
                I understand the purchase details and confirm that I want to proceed with this transaction.
                {exceedsRoundLimit && (
                  <span className="block text-orange-600 mt-1">
                    I acknowledge that excess payment will be automatically refunded.
                  </span>
                )}
              </label>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={handleClose}
              disabled={isProcessing}
              className="flex-1 py-3 px-4 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={isProcessing || hasInsufficientBalance || (!hasConfirmed && !isProcessing)}
              className={`flex-1 py-3 px-4 rounded-lg font-semibold transition-all ${
                isProcessing || hasInsufficientBalance || (!hasConfirmed && !isProcessing)
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-onion-500 to-onion-600 text-white hover:from-onion-600 hover:to-onion-700'
              }`}
            >
              {isProcessing ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Processing...</span>
                </div>
              ) : hasConfirmed ? (
                'Sign Transaction'
              ) : (
                'Confirm Details'
              )}
            </button>
          </div>

          {/* Risk Warning */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-sm">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
              <span className="font-medium text-yellow-800">Important Notice</span>
            </div>
            <div className="text-yellow-700 space-y-1">
              <p>• This transaction cannot be reversed once confirmed</p>
              <p>• Tokens will be distributed after successful auction completion</p>
              <p>• Refunds available during auction with 5% fee</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
