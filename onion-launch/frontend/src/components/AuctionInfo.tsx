'use client'

import { Calendar, Clock, TrendingUp, Info, Play, Pause, CheckCircle, XCircle } from 'lucide-react'
import { AUCTION_STATUS, AuctionStatus } from '@/hooks/useAuction'

interface AuctionInfoProps {
  data?: {
    startTime: number
    endTime: number
    softCap: number
    hardCap: number
    totalSupply: number
    currentPrice: number
    lastRoundPrice?: number
    auctionStatus: AuctionStatus
    currentRound: number
  }
}

export function AuctionInfo({ data }: AuctionInfoProps) {
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'UTC',
      timeZoneName: 'short'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  const formatTokens = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount) + ' ONION'
  }

  return (
    <div className="bg-white rounded-2xl card-shadow p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
          <Info className="w-5 h-5 text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Auction Information</h2>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Basic Info */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
            <Calendar className="w-5 h-5" />
            <span>Timeline</span>
          </h3>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Start Time:</span>
              <span className="font-medium text-gray-900">
                {data ? formatDate(data.startTime) : '2025-07-15 00:00 UTC'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">End Time:</span>
              <span className="font-medium text-gray-900">
                {data ? formatDate(data.endTime) : '2025-07-18 00:00 UTC'}
              </span>
            </div>
          </div>

          <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2 pt-4">
            <TrendingUp className="w-5 h-5" />
            <span>Caps & Supply</span>
          </h3>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Soft Cap:</span>
              <span className="font-medium text-green-600">
                {data ? formatCurrency(data.softCap) : formatCurrency(500000)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Hard Cap:</span>
              <span className="font-medium text-red-600">
                {data ? formatCurrency(data.hardCap) : formatCurrency(2000000)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Total Supply:</span>
              <span className="font-medium text-gray-900">
                {data ? formatTokens(data.totalSupply) : formatTokens(1000000)}
              </span>
            </div>
          </div>
        </div>

        {/* Pricing & Rules */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
            <Clock className="w-5 h-5" />
            <span>
              {data?.auctionStatus === AUCTION_STATUS.NOT_STARTED ? 'Starting Price' : 'Current Price'}
            </span>
          </h3>

          <div className="bg-gradient-to-r from-onion-50 to-onion-100 rounded-lg p-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-onion-700 mb-2">
                {data ? formatCurrency(data.currentPrice) : formatCurrency(0.11)}
              </div>
              <div className="text-sm text-onion-600">per ONION token</div>

              {/* Show price comparison for Round 2+ */}
              {data && data.currentRound > 1 && data.lastRoundPrice && (
                <div className="mt-3 pt-3 border-t border-onion-200">
                  <div className="flex items-center justify-center space-x-2 text-sm">
                    <span className="text-gray-600">Last Round:</span>
                    <span className="font-medium text-gray-700">
                      {formatCurrency(data.lastRoundPrice)}
                    </span>
                    <span className={`font-semibold ${
                      data.currentPrice > data.lastRoundPrice ? 'text-green-600' :
                      data.currentPrice < data.lastRoundPrice ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {data.currentPrice > data.lastRoundPrice ? '↗' :
                       data.currentPrice < data.lastRoundPrice ? '↘' : '→'}
                      {' '}
                      {(((data.currentPrice - data.lastRoundPrice) / data.lastRoundPrice) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <h3 className="text-lg font-semibold text-gray-900 pt-4">Auction Rules</h3>
          
          <div className="space-y-3 text-sm">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5 flex-shrink-0"></div>
              <div>
                <span className="font-medium">English Auction:</span> Price increases with time and demand
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-1.5 flex-shrink-0"></div>
              <div>
                <span className="font-medium">Fair & Transparent:</span> Equal opportunity for all participants
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-purple-500 rounded-full mt-1.5 flex-shrink-0"></div>
              <div>
                <span className="font-medium">Refund Available:</span> Request refund during auction (5% fee)
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-orange-500 rounded-full mt-1.5 flex-shrink-0"></div>
              <div>
                <span className="font-medium">Minimum Purchase:</span> 50 TON minimum investment
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Status Banner */}
      {data && (
        <div className={`mt-6 p-4 rounded-lg border ${
          data.auctionStatus === AUCTION_STATUS.NOT_STARTED ?
            'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200' :
          data.auctionStatus === AUCTION_STATUS.ACTIVE ?
            'bg-gradient-to-r from-green-50 to-green-100 border-green-200' :
          data.auctionStatus === AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS ?
            'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200' :
          data.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ?
            'bg-gradient-to-r from-green-50 to-green-100 border-green-200' :
          data.auctionStatus === AUCTION_STATUS.ENDED_FAILED ?
            'bg-gradient-to-r from-red-50 to-red-100 border-red-200' :
            'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {data.auctionStatus === AUCTION_STATUS.NOT_STARTED && (
                <>
                  <Clock className="w-5 h-5 text-blue-600" />
                  <span className="font-medium text-blue-800">Auction has not started yet</span>
                </>
              )}
              {data.auctionStatus === AUCTION_STATUS.ACTIVE && (
                <>
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <Play className="w-4 h-4 text-green-600" />
                  <span className="font-medium text-green-800">Auction is currently ACTIVE</span>
                </>
              )}
              {data.auctionStatus === AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS && (
                <>
                  <Pause className="w-5 h-5 text-yellow-600" />
                  <span className="font-medium text-yellow-800">Calculating next round price</span>
                </>
              )}
              {data.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS && (
                <>
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-800">Launch successful!</span>
                </>
              )}
              {data.auctionStatus === AUCTION_STATUS.ENDED_FAILED && (
                <>
                  <XCircle className="w-5 h-5 text-red-600" />
                  <span className="font-medium text-red-800">Launch failed</span>
                </>
              )}
            </div>
            <span className={`text-sm ${
              data.auctionStatus === AUCTION_STATUS.NOT_STARTED ? 'text-blue-600' :
              data.auctionStatus === AUCTION_STATUS.ACTIVE ? 'text-green-600' :
              data.auctionStatus === AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS ? 'text-yellow-600' :
              data.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ? 'text-green-600' :
              data.auctionStatus === AUCTION_STATUS.ENDED_FAILED ? 'text-red-600' :
              'text-gray-600'
            }`}>
              {data.auctionStatus === AUCTION_STATUS.NOT_STARTED ? 'Prepare to participate' :
               data.auctionStatus === AUCTION_STATUS.ACTIVE ? 'Accepting purchases now' :
               data.auctionStatus === AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS ? 'Please wait...' :
               data.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ? 'Soft cap reached' :
               data.auctionStatus === AUCTION_STATUS.ENDED_FAILED ? 'Soft cap not reached' :
               'Status unknown'}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}