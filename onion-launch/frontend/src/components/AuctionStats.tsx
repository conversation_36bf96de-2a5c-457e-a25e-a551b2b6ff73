'use client'

import { useState, useEffect } from 'react'
import { Clock, TrendingUp, Target, DollarSign, Play, Pause, CheckCircle, XCircle } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { AUCTION_STATUS, AuctionStatus } from '@/hooks/useAuction'

interface AuctionStatsProps {
  data?: {
    totalRaised: number
    totalTokensSold: number
    auctionStatus: AuctionStatus
    currentRound: number
    currentPrice: number
    lastRoundPrice?: number
    endTime: number
    roundEndTime?: number
    nextRoundStartTime?: number
    softCap: number
    hardCap: number
    totalSupply: number
    startTime: number
    remainingTokensInRound?: number
  }
}

export function AuctionStats({ data }: AuctionStatsProps) {
  const [timeLeft, setTimeLeft] = useState('')
  const [timeLabel, setTimeLabel] = useState('Time Left')

  useEffect(() => {
    if (!data) return

    const timer = setInterval(() => {
      const now = Date.now()

      // Handle different auction states
      switch (data.auctionStatus) {
        case AUCTION_STATUS.NOT_STARTED:
          const startTime = data.startTime * 1000
          if (now >= startTime) {
            setTimeLeft('Starting...')
            setTimeLabel('Status')
          } else {
            setTimeLeft(formatDistanceToNow(startTime))
            setTimeLabel('Starts In')
          }
          break

        case AUCTION_STATUS.ACTIVE:
          const roundEndTime = (data.roundEndTime || data.endTime) * 1000
          if (now >= roundEndTime) {
            setTimeLeft('Round Ending...')
            setTimeLabel('Status')
          } else {
            setTimeLeft(formatDistanceToNow(roundEndTime))
            setTimeLabel('Round Ends In')
          }
          break

        case AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS:
          const nextRoundTime = data.nextRoundStartTime! * 1000
          if (now >= nextRoundTime) {
            setTimeLeft('Starting Next Round...')
            setTimeLabel('Status')
          } else {
            setTimeLeft(formatDistanceToNow(nextRoundTime))
            setTimeLabel('Next Round In')
          }
          break

        case AUCTION_STATUS.ENDED_SUCCESS:
        case AUCTION_STATUS.ENDED_FAILED:
          setTimeLeft('Ended')
          setTimeLabel('Status')
          break

        default:
          const endTime = data.endTime * 1000
          if (now >= endTime) {
            setTimeLeft('Auction Ended')
            setTimeLabel('Status')
          } else {
            setTimeLeft(formatDistanceToNow(endTime))
            setTimeLabel('Time Left')
          }
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [data])

  const progress = data ? (data.totalRaised / data.hardCap) * 100 : 0
  const tokenProgress = data ? (data.totalTokensSold / data.totalSupply) * 100 : 0

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatTokens = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount) + ' ONION'
  }

  const getStatusInfo = () => {
    if (!data) return { color: 'gray', text: 'Loading...', icon: Clock }

    switch (data.auctionStatus) {
      case AUCTION_STATUS.NOT_STARTED:
        return { color: 'blue', text: 'Not Started', icon: Clock }
      case AUCTION_STATUS.ACTIVE:
        return { color: 'green', text: 'Active', icon: Play }
      case AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS:
        return { color: 'yellow', text: 'Calculating Next Round', icon: Pause }
      case AUCTION_STATUS.ENDED_SUCCESS:
        return { color: 'green', text: 'Launch Successful', icon: CheckCircle }
      case AUCTION_STATUS.ENDED_FAILED:
        return { color: 'red', text: 'Launch Failed', icon: XCircle }
      default:
        return { color: 'gray', text: 'Unknown', icon: Clock }
    }
  }

  const statusInfo = getStatusInfo()

  return (
    <div className="bg-white rounded-2xl card-shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Live Auction Stats</h2>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 bg-${statusInfo.color}-500 rounded-full ${
            data?.auctionStatus === AUCTION_STATUS.ACTIVE ? 'animate-pulse' : ''
          }`}></div>
          <statusInfo.icon className={`w-4 h-4 text-${statusInfo.color}-600`} />
          <span className={`text-sm font-medium text-${statusInfo.color}-600`}>
            {statusInfo.text}
          </span>
        </div>
      </div>

      {/* Main Stats Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl mb-3 mx-auto">
            <DollarSign className="w-6 h-6 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {data ? formatCurrency(data.totalRaised) : formatCurrency(1600000)}
          </div>
          <div className="text-sm text-gray-500">Total Raised</div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl mb-3 mx-auto">
            <Target className="w-6 h-6 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {data ? formatTokens(data.totalTokensSold) : formatTokens(673000)}
          </div>
          <div className="text-sm text-gray-500">Tokens Sold</div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl mb-3 mx-auto">
            <TrendingUp className="w-6 h-6 text-purple-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            Round {data?.currentRound || 12}
          </div>
          <div className="text-sm text-gray-500">Current Round</div>
        </div>

        <div className="text-center">
          <div className={`flex items-center justify-center w-12 h-12 bg-${statusInfo.color}-100 rounded-xl mb-3 mx-auto`}>
            <Clock className={`w-6 h-6 text-${statusInfo.color}-600`} />
          </div>
          <div className="text-lg font-bold text-gray-900">
            {timeLeft || '1d 12h'}
          </div>
          <div className="text-sm text-gray-500">{timeLabel}</div>
        </div>
      </div>

      {/* Progress Bars */}
      <div className="space-y-6">
        {/* Funding Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Funding Progress</span>
            <span className={`text-sm font-bold ${
              data?.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ? 'text-blue-600' :
              data?.auctionStatus === AUCTION_STATUS.ENDED_FAILED ? 'text-red-600' :
              'text-gray-900'
            }`}>
              {progress.toFixed(1)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-500 ease-out ${
                data?.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS ? 'bg-blue-500' :
                data?.auctionStatus === AUCTION_STATUS.ENDED_FAILED ? 'bg-red-500' :
                'progress-bar'
              }`}
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Soft Cap: {data ? formatCurrency(data.softCap) : formatCurrency(500000)}</span>
            <span>Hard Cap: {data ? formatCurrency(data.hardCap) : formatCurrency(2000000)}</span>
          </div>
        </div>

        {/* Token Distribution Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Token Distribution</span>
            <span className="text-sm font-bold text-gray-900">{tokenProgress.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-onion-500 to-onion-600 h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${Math.min(tokenProgress, 100)}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0 ONION</span>
            <span>{data ? formatTokens(data.totalSupply) : formatTokens(1000000)}</span>
          </div>
        </div>

        {/* Status-specific alerts */}
        {data?.auctionStatus === AUCTION_STATUS.NOT_STARTED && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">
                Auction will start {timeLeft}. Get ready to participate!
              </span>
            </div>
          </div>
        )}

        {data?.auctionStatus === AUCTION_STATUS.PAUSED_BETWEEN_ROUNDS && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Pause className="w-5 h-5 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">
                Calculating new round price. Next round starts {timeLeft}.
              </span>
            </div>
          </div>
        )}

        {data?.auctionStatus === AUCTION_STATUS.ENDED_SUCCESS && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-green-800">
                🎉 Launch successful! Soft cap reached. Tokens will be distributed soon.
              </span>
            </div>
          </div>
        )}

        {data?.auctionStatus === AUCTION_STATUS.ENDED_FAILED && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="w-5 h-5 text-red-600" />
              <span className="text-sm font-medium text-red-800">
                Launch failed. Soft cap not reached. Refunds will be processed.
              </span>
            </div>
          </div>
        )}

        {/* Remaining Tokens Alert for active auction */}
        {data?.auctionStatus === AUCTION_STATUS.ACTIVE && tokenProgress > 80 && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">
                Only {data ? formatTokens(data.totalSupply - data.totalTokensSold) : formatTokens(327000)} tokens remaining!
              </span>
            </div>
          </div>
        )}

        {/* Round-specific info for active auction */}
        {data?.auctionStatus === AUCTION_STATUS.ACTIVE && data.remainingTokensInRound && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">
                {formatTokens(data.remainingTokensInRound)} tokens available in current round
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}