'use client'

import { CheckCircle, XCircle, TrendingUp, Users, Clock, Gift, AlertTriangle } from 'lucide-react'
import { AUCTION_STATUS, AuctionStatus } from '@/hooks/useAuction'

interface AuctionEndedStatusProps {
  auctionStatus: AuctionStatus
  totalRaised: number
  softCap: number
  hardCap: number
  totalTokensSold: number
  totalSupply: number
  currentRound: number
  finalPrice: number
  startPrice?: number
}

export function AuctionEndedStatus({
  auctionStatus,
  totalRaised,
  softCap,
  hardCap,
  totalTokensSold,
  totalSupply,
  currentRound,
  finalPrice,
  startPrice = 0.05
}: AuctionEndedStatusProps) {
  const isSuccess = auctionStatus === AUCTION_STATUS.ENDED_SUCCESS
  const progress = (totalRaised / hardCap) * 100
  const softCapReached = totalRaised >= softCap
  const priceGrowth = startPrice ? ((finalPrice - startPrice) / startPrice) * 100 : 0

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatTokens = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount) + ' ONION'
  }

  return (
    <div className="bg-white rounded-2xl card-shadow p-8">
      <div className="text-center">
        {/* Status Icon and Title */}
        <div className={`flex items-center justify-center w-20 h-20 rounded-full mb-6 mx-auto ${
          isSuccess ? 'bg-green-100' : 'bg-red-100'
        }`}>
          {isSuccess ? (
            <CheckCircle className="w-10 h-10 text-green-600" />
          ) : (
            <XCircle className="w-10 h-10 text-red-600" />
          )}
        </div>

        <h2 className={`text-3xl font-bold mb-4 ${
          isSuccess ? 'text-green-800' : 'text-red-800'
        }`}>
          {isSuccess ? '🎉 Launch Successful!' : '😞 Launch Failed'}
        </h2>

        <p className={`text-lg mb-8 ${
          isSuccess ? 'text-green-700' : 'text-red-700'
        }`}>
          {isSuccess 
            ? 'Congratulations! The ONION token fair launch has successfully reached its soft cap.'
            : 'Unfortunately, the ONION token fair launch did not reach the minimum funding requirement.'
          }
        </p>

        {/* Key Metrics */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Raised */}
          <div className="text-center">
            <div className={`text-3xl font-bold mb-2 ${
              isSuccess ? 'text-blue-600' : 'text-red-600'
            }`}>
              {formatCurrency(totalRaised)}
            </div>
            <div className="text-sm text-gray-500">Total Raised</div>
            <div className={`text-xs mt-1 ${
              softCapReached ? 'text-green-600' : 'text-red-600'
            }`}>
              {softCapReached ? '✅ Soft cap reached' : '❌ Below soft cap'}
            </div>
          </div>

          {/* Tokens Sold */}
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">
              {formatTokens(totalTokensSold)}
            </div>
            <div className="text-sm text-gray-500">Tokens Sold</div>
            <div className="text-xs text-gray-600 mt-1">
              {((totalTokensSold / totalSupply) * 100).toFixed(1)}% of supply
            </div>
          </div>

          {/* Final Round */}
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">
              {currentRound}
            </div>
            <div className="text-sm text-gray-500">Final Round</div>
            <div className="text-xs text-gray-600 mt-1">
              Completed rounds
            </div>
          </div>

          {/* Price Growth */}
          <div className="text-center">
            <div className={`text-3xl font-bold mb-2 ${
              priceGrowth > 0 ? 'text-green-600' : 'text-gray-600'
            }`}>
              +{priceGrowth.toFixed(0)}%
            </div>
            <div className="text-sm text-gray-500">Price Growth</div>
            <div className="text-xs text-gray-600 mt-1">
              ${startPrice.toFixed(3)} → ${finalPrice.toFixed(3)}
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Funding Progress</span>
            <span className={`text-sm font-bold ${
              isSuccess ? 'text-blue-600' : 'text-red-600'
            }`}>
              {progress.toFixed(1)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-4">
            <div 
              className={`h-4 rounded-full transition-all duration-500 ease-out ${
                isSuccess ? 'bg-blue-500' : 'bg-red-500'
              }`}
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Soft Cap: {formatCurrency(softCap)}</span>
            <span>Hard Cap: {formatCurrency(hardCap)}</span>
          </div>
        </div>

        {/* Next Steps */}
        <div className={`rounded-lg p-6 mb-6 ${
          isSuccess ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
        }`}>
          <h3 className={`text-lg font-semibold mb-4 ${
            isSuccess ? 'text-green-800' : 'text-red-800'
          }`}>
            What happens next?
          </h3>
          
          {isSuccess ? (
            <div className="space-y-3 text-sm text-green-700">
              <div className="flex items-center space-x-3">
                <Gift className="w-5 h-5 text-green-600" />
                <span>Token distribution will begin within 24 hours</span>
              </div>
              <div className="flex items-center space-x-3">
                <TrendingUp className="w-5 h-5 text-green-600" />
                <span>ONION tokens will be available for trading on DEX platforms</span>
              </div>
              <div className="flex items-center space-x-3">
                <Users className="w-5 h-5 text-green-600" />
                <span>Community governance and staking features will be activated</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>Project development roadmap will commence as planned</span>
              </div>
            </div>
          ) : (
            <div className="space-y-3 text-sm text-red-700">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <span>All participant funds will be automatically refunded</span>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="w-5 h-5 text-red-600" />
                <span>Refund processing will begin within 24 hours</span>
              </div>
              <div className="flex items-center space-x-3">
                <XCircle className="w-5 h-5 text-red-600" />
                <span>No tokens will be distributed as soft cap was not reached</span>
              </div>
              <div className="flex items-center space-x-3">
                <Users className="w-5 h-5 text-red-600" />
                <span>Project team will reassess launch strategy and timing</span>
              </div>
            </div>
          )}
        </div>

        {/* Call to Action */}
        <div className="space-y-4">
          {isSuccess ? (
            <>
              <button className="w-full py-4 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-green-700 transition-all">
                View Your Tokens
              </button>
              <button className="w-full py-3 border border-green-500 text-green-600 font-medium rounded-lg hover:bg-green-50 transition-all">
                Join Community Discord
              </button>
            </>
          ) : (
            <>
              <button className="w-full py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all">
                Check Refund Status
              </button>
              <button className="w-full py-3 border border-blue-500 text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-all">
                Stay Updated on Relaunch
              </button>
            </>
          )}
        </div>

        {/* Thank You Message */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-gray-600">
            Thank you to all {Math.floor(totalTokensSold / 1000)} participants who joined the ONION token fair launch. 
            {isSuccess 
              ? ' Your support has made this project possible!'
              : ' Your interest and participation are greatly appreciated.'
            }
          </p>
        </div>
      </div>
    </div>
  )
}
