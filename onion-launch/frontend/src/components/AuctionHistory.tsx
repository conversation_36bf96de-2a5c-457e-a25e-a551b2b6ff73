'use client'

import { useState, useEffect } from 'react'
import { History, TrendingUp, Clock, RefreshCw } from 'lucide-react'
import { useAuction } from '@/hooks/useAuction'

interface Round {
  id: number
  timeRange: string
  price: number
  volume: number
  endTime: string
  status: 'active' | 'completed' | 'upcoming'
}

interface AuctionHistoryProps {
  maxRounds?: number
}

export function AuctionHistory({ maxRounds = 10 }: AuctionHistoryProps) {
  const { auctionData } = useAuction()
  const [rounds, setRounds] = useState<Round[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Generate rounds data based on current auction state
  useEffect(() => {
    if (!auctionData) return

    setIsLoading(true)

    // In a real app, this would fetch historical data from the blockchain or API
    // For now, we'll generate mock data based on the current round
    const currentRound = auctionData.currentRound
    const currentPrice = auctionData.currentPrice
    const lastRoundPrice = auctionData.lastRoundPrice || (currentPrice * 0.9)

    const mockRounds: Round[] = []

    // Current round
    mockRounds.push({
      id: currentRound,
      timeRange: `${new Date(Date.now() - 3600000).toLocaleTimeString()} - ${new Date(Date.now() + 3600000).toLocaleTimeString()} UTC`,
      price: currentPrice,
      volume: Math.floor(auctionData.totalTokensSold * 0.1),
      endTime: 'Active',
      status: 'active'
    })

    // Previous rounds
    for (let i = 1; i <= Math.min(currentRound - 1, maxRounds - 1); i++) {
      const roundId = currentRound - i
      const roundPrice = lastRoundPrice * Math.pow(0.9, i - 1)
      const hourAgo = new Date(Date.now() - (i + 1) * 3600000)
      const twoHoursAgo = new Date(Date.now() - (i + 2) * 3600000)

      mockRounds.push({
        id: roundId,
        timeRange: `${twoHoursAgo.toLocaleTimeString()} - ${hourAgo.toLocaleTimeString()} UTC`,
        price: roundPrice,
        volume: Math.floor(20000 + Math.random() * 30000),
        endTime: hourAgo.toLocaleTimeString(),
        status: 'completed'
      })
    }

    setRounds(mockRounds)
    setIsLoading(false)
  }, [auctionData, maxRounds])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3,
    }).format(amount)
  }

  const formatTokens = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getPriceChangeColor = (currentRound: Round, index: number) => {
    if (index === rounds.length - 1) return 'text-gray-500'
    const nextRound = rounds[index + 1]
    if (currentRound.price > nextRound.price) return 'text-green-600'
    if (currentRound.price < nextRound.price) return 'text-red-600'
    return 'text-gray-500'
  }

  const getPriceChangeIcon = (currentRound: Round, index: number) => {
    if (index === rounds.length - 1) return null
    const nextRound = rounds[index + 1]
    if (currentRound.price > nextRound.price) return '↗'
    if (currentRound.price < nextRound.price) return '↘'
    return '→'
  }

  const refreshHistory = () => {
    // In a real app, this would refetch data from the API
    if (auctionData) {
      setIsLoading(true)
      // Simulate API delay
      setTimeout(() => setIsLoading(false), 1000)
    }
  }

  return (
    <div className="bg-white rounded-2xl card-shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-lg">
            <History className="w-5 h-5 text-purple-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900">Auction History</h2>
        </div>
        <button
          onClick={refreshHistory}
          disabled={isLoading}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
          title="Refresh history"
        >
          <RefreshCw className={`w-5 h-5 text-gray-500 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <span className="ml-3 text-gray-600">Loading auction history...</span>
        </div>
      ) : rounds.length === 0 ? (
        <div className="text-center py-12">
          <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No auction history available yet.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 text-sm font-medium text-gray-600">Round</th>
                <th className="text-left py-3 text-sm font-medium text-gray-600">Time Range (UTC)</th>
                <th className="text-left py-3 text-sm font-medium text-gray-600">Price</th>
                <th className="text-left py-3 text-sm font-medium text-gray-600">Volume</th>
                <th className="text-left py-3 text-sm font-medium text-gray-600">Status</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {rounds.map((round, index) => (
              <tr
                key={round.id}
                className={`hover:bg-gray-50 transition-colors ${
                  round.status === 'active' ? 'bg-green-50' : ''
                }`}
              >
                <td className="py-4">
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-900">Round {round.id}</span>
                    {round.status === 'active' && (
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    )}
                  </div>
                </td>
                <td className="py-4">
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <Clock className="w-3 h-3" />
                    <span>{round.timeRange}</span>
                  </div>
                </td>
                <td className="py-4">
                  <div className="flex items-center space-x-2">
                    <span className={`font-semibold ${getPriceChangeColor(round, index)}`}>
                      {formatCurrency(round.price)}
                    </span>
                    {getPriceChangeIcon(round, index) && (
                      <span className={`text-xs ${getPriceChangeColor(round, index)}`}>
                        {getPriceChangeIcon(round, index)}
                      </span>
                    )}
                  </div>
                </td>
                <td className="py-4">
                  <div className="flex items-center space-x-1">
                    <span className="font-medium text-gray-900">
                      {formatTokens(round.volume)}
                    </span>
                    <span className="text-xs text-gray-500">ONION</span>
                  </div>
                </td>
                <td className="py-4">
                  {round.status === 'active' ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5 animate-pulse"></div>
                      Active
                    </span>
                  ) : round.status === 'completed' ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Ended {round.endTime}
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Upcoming
                    </span>
                  )}
                </td>
              </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Summary Stats */}
      {!isLoading && rounds.length > 0 && (
        <div className="mt-6 grid grid-cols-3 gap-4 pt-6 border-t border-gray-200">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{auctionData?.currentRound || 0}</div>
            <div className="text-sm text-gray-500">Current Round</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {rounds.length > 1 ?
                `+${(((rounds[0].price - rounds[rounds.length - 1].price) / rounds[rounds.length - 1].price) * 100).toFixed(0)}%` :
                'N/A'
              }
            </div>
            <div className="text-sm text-gray-500">Price Growth</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {formatTokens(rounds.reduce((sum, round) => sum + round.volume, 0))}
            </div>
            <div className="text-sm text-gray-500">Total Volume</div>
          </div>
        </div>
      )}
    </div>
  )
}