'use client'

import { useState, useEffect } from 'react'
import { Clock, TrendingUp, Calculator, Zap, ArrowRight } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface RoundTransitionProps {
  currentRound: number
  nextRoundStartTime: number
  lastRoundPrice?: number
  estimatedNextPrice?: number
  isCalculating?: boolean
}

export function RoundTransition({
  currentRound,
  nextRoundStartTime,
  lastRoundPrice,
  estimatedNextPrice,
  isCalculating = true
}: RoundTransitionProps) {
  const [timeLeft, setTimeLeft] = useState('')
  const [calculationProgress, setCalculationProgress] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      const now = Date.now()
      const startTime = nextRoundStartTime * 1000
      
      if (now >= startTime) {
        setTimeLeft('Starting...')
        clearInterval(timer)
        return
      }

      setTimeLeft(formatDistanceToNow(startTime))
    }, 1000)

    return () => clearInterval(timer)
  }, [nextRoundStartTime])

  // Simulate calculation progress
  useEffect(() => {
    if (!isCalculating) {
      setCalculationProgress(100)
      return
    }

    const progressTimer = setInterval(() => {
      setCalculationProgress(prev => {
        if (prev >= 95) return prev // Stop at 95% until real calculation is done
        return prev + Math.random() * 5
      })
    }, 500)

    return () => clearInterval(progressTimer)
  }, [isCalculating])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3,
    }).format(amount)
  }

  const priceChange = lastRoundPrice && estimatedNextPrice ? 
    ((estimatedNextPrice - lastRoundPrice) / lastRoundPrice) * 100 : 0

  return (
    <div className="bg-white rounded-2xl card-shadow p-6">
      <div className="text-center">
        {/* Header */}
        <div className="flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-6 mx-auto">
          <Calculator className="w-8 h-8 text-yellow-600" />
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Calculating Round {currentRound + 1} Price
        </h2>
        
        <p className="text-gray-600 mb-8">
          The auction is temporarily paused while we calculate the optimal price for the next round based on current demand and market conditions.
        </p>

        {/* Calculation Progress */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Calculation Progress</span>
            <span className="text-sm font-bold text-yellow-600">
              {Math.round(calculationProgress)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${Math.min(calculationProgress, 100)}%` }}
            />
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {isCalculating ? 'Analyzing market data and demand patterns...' : 'Calculation complete!'}
          </div>
        </div>

        {/* Price Comparison */}
        {lastRoundPrice && (
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Price Analysis</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
              {/* Last Round Price */}
              <div className="text-center">
                <div className="text-sm text-gray-600 mb-1">Round {currentRound} Price</div>
                <div className="text-xl font-bold text-gray-900">
                  {formatCurrency(lastRoundPrice)}
                </div>
              </div>

              {/* Arrow */}
              <div className="flex justify-center">
                <ArrowRight className="w-6 h-6 text-gray-400" />
              </div>

              {/* Next Round Price */}
              <div className="text-center">
                <div className="text-sm text-gray-600 mb-1">Round {currentRound + 1} Price</div>
                {estimatedNextPrice ? (
                  <div className="text-xl font-bold text-yellow-600">
                    {formatCurrency(estimatedNextPrice)}
                  </div>
                ) : (
                  <div className="text-xl font-bold text-gray-400">
                    Calculating...
                  </div>
                )}
                {estimatedNextPrice && (
                  <div className={`text-sm font-medium ${
                    priceChange > 0 ? 'text-green-600' : priceChange < 0 ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {priceChange > 0 ? '+' : ''}{priceChange.toFixed(1)}%
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Countdown Timer */}
        <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-center space-x-3 mb-3">
            <Clock className="w-6 h-6 text-yellow-600" />
            <span className="text-lg font-semibold text-yellow-800">Next Round Starts In</span>
          </div>
          <div className="text-3xl font-bold text-yellow-700 mb-2">
            {timeLeft}
          </div>
          <div className="text-sm text-yellow-600">
            Round {currentRound + 1} will begin automatically
          </div>
        </div>

        {/* Calculation Steps */}
        <div className="text-left">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Price Calculation Process</h3>
          
          <div className="space-y-3">
            <div className={`flex items-center space-x-3 ${
              calculationProgress > 20 ? 'text-green-600' : 'text-gray-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                calculationProgress > 20 ? 'bg-green-500' : 'bg-gray-300'
              }`}></div>
              <span className="text-sm">Analyzing current round demand</span>
              {calculationProgress > 20 && <Zap className="w-4 h-4" />}
            </div>
            
            <div className={`flex items-center space-x-3 ${
              calculationProgress > 40 ? 'text-green-600' : 'text-gray-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                calculationProgress > 40 ? 'bg-green-500' : 'bg-gray-300'
              }`}></div>
              <span className="text-sm">Calculating optimal price increase</span>
              {calculationProgress > 40 && <Zap className="w-4 h-4" />}
            </div>
            
            <div className={`flex items-center space-x-3 ${
              calculationProgress > 60 ? 'text-green-600' : 'text-gray-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                calculationProgress > 60 ? 'bg-green-500' : 'bg-gray-300'
              }`}></div>
              <span className="text-sm">Validating market conditions</span>
              {calculationProgress > 60 && <Zap className="w-4 h-4" />}
            </div>
            
            <div className={`flex items-center space-x-3 ${
              calculationProgress > 80 ? 'text-green-600' : 'text-gray-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                calculationProgress > 80 ? 'bg-green-500' : 'bg-gray-300'
              }`}></div>
              <span className="text-sm">Finalizing next round parameters</span>
              {calculationProgress > 80 && <Zap className="w-4 h-4" />}
            </div>
            
            <div className={`flex items-center space-x-3 ${
              calculationProgress >= 100 ? 'text-green-600' : 'text-gray-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                calculationProgress >= 100 ? 'bg-green-500' : 'bg-gray-300'
              }`}></div>
              <span className="text-sm">Ready to start next round</span>
              {calculationProgress >= 100 && <Zap className="w-4 h-4" />}
            </div>
          </div>
        </div>

        {/* Info Box */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <TrendingUp className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">Why the pause?</span>
          </div>
          <div className="text-sm text-blue-700">
            <p>
              Our English auction mechanism uses dynamic pricing to ensure fair market discovery. 
              Between rounds, we analyze demand patterns and adjust pricing to maintain optimal 
              token distribution while maximizing value for all participants.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
