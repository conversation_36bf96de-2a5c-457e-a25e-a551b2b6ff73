/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tweetnacl";
exports.ids = ["vendor-chunks/tweetnacl"];
exports.modules = {

/***/ "(ssr)/./node_modules/tweetnacl/nacl-fast.js":
/*!*********************************************!*\
  !*** ./node_modules/tweetnacl/nacl-fast.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("(function(nacl) {\n'use strict';\n\n// Ported in 2014 by Dmitry Chestnykh and Devi Mandiri.\n// Public domain.\n//\n// Implementation derived from TweetNaCl version 20140427.\n// See for details: http://tweetnacl.cr.yp.to/\n\nvar gf = function(init) {\n  var i, r = new Float64Array(16);\n  if (init) for (i = 0; i < init.length; i++) r[i] = init[i];\n  return r;\n};\n\n//  Pluggable, initialized in high-level API below.\nvar randombytes = function(/* x, n */) { throw new Error('no PRNG'); };\n\nvar _0 = new Uint8Array(16);\nvar _9 = new Uint8Array(32); _9[0] = 9;\n\nvar gf0 = gf(),\n    gf1 = gf([1]),\n    _121665 = gf([0xdb41, 1]),\n    D = gf([0x78a3, 0x1359, 0x4dca, 0x75eb, 0xd8ab, 0x4141, 0x0a4d, 0x0070, 0xe898, 0x7779, 0x4079, 0x8cc7, 0xfe73, 0x2b6f, 0x6cee, 0x5203]),\n    D2 = gf([0xf159, 0x26b2, 0x9b94, 0xebd6, 0xb156, 0x8283, 0x149a, 0x00e0, 0xd130, 0xeef3, 0x80f2, 0x198e, 0xfce7, 0x56df, 0xd9dc, 0x2406]),\n    X = gf([0xd51a, 0x8f25, 0x2d60, 0xc956, 0xa7b2, 0x9525, 0xc760, 0x692c, 0xdc5c, 0xfdd6, 0xe231, 0xc0a4, 0x53fe, 0xcd6e, 0x36d3, 0x2169]),\n    Y = gf([0x6658, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666]),\n    I = gf([0xa0b0, 0x4a0e, 0x1b27, 0xc4ee, 0xe478, 0xad2f, 0x1806, 0x2f43, 0xd7a7, 0x3dfb, 0x0099, 0x2b4d, 0xdf0b, 0x4fc1, 0x2480, 0x2b83]);\n\nfunction ts64(x, i, h, l) {\n  x[i]   = (h >> 24) & 0xff;\n  x[i+1] = (h >> 16) & 0xff;\n  x[i+2] = (h >>  8) & 0xff;\n  x[i+3] = h & 0xff;\n  x[i+4] = (l >> 24)  & 0xff;\n  x[i+5] = (l >> 16)  & 0xff;\n  x[i+6] = (l >>  8)  & 0xff;\n  x[i+7] = l & 0xff;\n}\n\nfunction vn(x, xi, y, yi, n) {\n  var i,d = 0;\n  for (i = 0; i < n; i++) d |= x[xi+i]^y[yi+i];\n  return (1 & ((d - 1) >>> 8)) - 1;\n}\n\nfunction crypto_verify_16(x, xi, y, yi) {\n  return vn(x,xi,y,yi,16);\n}\n\nfunction crypto_verify_32(x, xi, y, yi) {\n  return vn(x,xi,y,yi,32);\n}\n\nfunction core_salsa20(o, p, k, c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n   x0 =  x0 +  j0 | 0;\n   x1 =  x1 +  j1 | 0;\n   x2 =  x2 +  j2 | 0;\n   x3 =  x3 +  j3 | 0;\n   x4 =  x4 +  j4 | 0;\n   x5 =  x5 +  j5 | 0;\n   x6 =  x6 +  j6 | 0;\n   x7 =  x7 +  j7 | 0;\n   x8 =  x8 +  j8 | 0;\n   x9 =  x9 +  j9 | 0;\n  x10 = x10 + j10 | 0;\n  x11 = x11 + j11 | 0;\n  x12 = x12 + j12 | 0;\n  x13 = x13 + j13 | 0;\n  x14 = x14 + j14 | 0;\n  x15 = x15 + j15 | 0;\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x1 >>>  0 & 0xff;\n  o[ 5] = x1 >>>  8 & 0xff;\n  o[ 6] = x1 >>> 16 & 0xff;\n  o[ 7] = x1 >>> 24 & 0xff;\n\n  o[ 8] = x2 >>>  0 & 0xff;\n  o[ 9] = x2 >>>  8 & 0xff;\n  o[10] = x2 >>> 16 & 0xff;\n  o[11] = x2 >>> 24 & 0xff;\n\n  o[12] = x3 >>>  0 & 0xff;\n  o[13] = x3 >>>  8 & 0xff;\n  o[14] = x3 >>> 16 & 0xff;\n  o[15] = x3 >>> 24 & 0xff;\n\n  o[16] = x4 >>>  0 & 0xff;\n  o[17] = x4 >>>  8 & 0xff;\n  o[18] = x4 >>> 16 & 0xff;\n  o[19] = x4 >>> 24 & 0xff;\n\n  o[20] = x5 >>>  0 & 0xff;\n  o[21] = x5 >>>  8 & 0xff;\n  o[22] = x5 >>> 16 & 0xff;\n  o[23] = x5 >>> 24 & 0xff;\n\n  o[24] = x6 >>>  0 & 0xff;\n  o[25] = x6 >>>  8 & 0xff;\n  o[26] = x6 >>> 16 & 0xff;\n  o[27] = x6 >>> 24 & 0xff;\n\n  o[28] = x7 >>>  0 & 0xff;\n  o[29] = x7 >>>  8 & 0xff;\n  o[30] = x7 >>> 16 & 0xff;\n  o[31] = x7 >>> 24 & 0xff;\n\n  o[32] = x8 >>>  0 & 0xff;\n  o[33] = x8 >>>  8 & 0xff;\n  o[34] = x8 >>> 16 & 0xff;\n  o[35] = x8 >>> 24 & 0xff;\n\n  o[36] = x9 >>>  0 & 0xff;\n  o[37] = x9 >>>  8 & 0xff;\n  o[38] = x9 >>> 16 & 0xff;\n  o[39] = x9 >>> 24 & 0xff;\n\n  o[40] = x10 >>>  0 & 0xff;\n  o[41] = x10 >>>  8 & 0xff;\n  o[42] = x10 >>> 16 & 0xff;\n  o[43] = x10 >>> 24 & 0xff;\n\n  o[44] = x11 >>>  0 & 0xff;\n  o[45] = x11 >>>  8 & 0xff;\n  o[46] = x11 >>> 16 & 0xff;\n  o[47] = x11 >>> 24 & 0xff;\n\n  o[48] = x12 >>>  0 & 0xff;\n  o[49] = x12 >>>  8 & 0xff;\n  o[50] = x12 >>> 16 & 0xff;\n  o[51] = x12 >>> 24 & 0xff;\n\n  o[52] = x13 >>>  0 & 0xff;\n  o[53] = x13 >>>  8 & 0xff;\n  o[54] = x13 >>> 16 & 0xff;\n  o[55] = x13 >>> 24 & 0xff;\n\n  o[56] = x14 >>>  0 & 0xff;\n  o[57] = x14 >>>  8 & 0xff;\n  o[58] = x14 >>> 16 & 0xff;\n  o[59] = x14 >>> 24 & 0xff;\n\n  o[60] = x15 >>>  0 & 0xff;\n  o[61] = x15 >>>  8 & 0xff;\n  o[62] = x15 >>> 16 & 0xff;\n  o[63] = x15 >>> 24 & 0xff;\n}\n\nfunction core_hsalsa20(o,p,k,c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x5 >>>  0 & 0xff;\n  o[ 5] = x5 >>>  8 & 0xff;\n  o[ 6] = x5 >>> 16 & 0xff;\n  o[ 7] = x5 >>> 24 & 0xff;\n\n  o[ 8] = x10 >>>  0 & 0xff;\n  o[ 9] = x10 >>>  8 & 0xff;\n  o[10] = x10 >>> 16 & 0xff;\n  o[11] = x10 >>> 24 & 0xff;\n\n  o[12] = x15 >>>  0 & 0xff;\n  o[13] = x15 >>>  8 & 0xff;\n  o[14] = x15 >>> 16 & 0xff;\n  o[15] = x15 >>> 24 & 0xff;\n\n  o[16] = x6 >>>  0 & 0xff;\n  o[17] = x6 >>>  8 & 0xff;\n  o[18] = x6 >>> 16 & 0xff;\n  o[19] = x6 >>> 24 & 0xff;\n\n  o[20] = x7 >>>  0 & 0xff;\n  o[21] = x7 >>>  8 & 0xff;\n  o[22] = x7 >>> 16 & 0xff;\n  o[23] = x7 >>> 24 & 0xff;\n\n  o[24] = x8 >>>  0 & 0xff;\n  o[25] = x8 >>>  8 & 0xff;\n  o[26] = x8 >>> 16 & 0xff;\n  o[27] = x8 >>> 24 & 0xff;\n\n  o[28] = x9 >>>  0 & 0xff;\n  o[29] = x9 >>>  8 & 0xff;\n  o[30] = x9 >>> 16 & 0xff;\n  o[31] = x9 >>> 24 & 0xff;\n}\n\nfunction crypto_core_salsa20(out,inp,k,c) {\n  core_salsa20(out,inp,k,c);\n}\n\nfunction crypto_core_hsalsa20(out,inp,k,c) {\n  core_hsalsa20(out,inp,k,c);\n}\n\nvar sigma = new Uint8Array([101, 120, 112, 97, 110, 100, 32, 51, 50, 45, 98, 121, 116, 101, 32, 107]);\n            // \"expand 32-byte k\"\n\nfunction crypto_stream_salsa20_xor(c,cpos,m,mpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n    mpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream_salsa20(c,cpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream(c,cpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20(c,cpos,d,sn,s);\n}\n\nfunction crypto_stream_xor(c,cpos,m,mpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20_xor(c,cpos,m,mpos,d,sn,s);\n}\n\n/*\n* Port of Andrew Moon's Poly1305-donna-16. Public domain.\n* https://github.com/floodyberry/poly1305-donna\n*/\n\nvar poly1305 = function(key) {\n  this.buffer = new Uint8Array(16);\n  this.r = new Uint16Array(10);\n  this.h = new Uint16Array(10);\n  this.pad = new Uint16Array(8);\n  this.leftover = 0;\n  this.fin = 0;\n\n  var t0, t1, t2, t3, t4, t5, t6, t7;\n\n  t0 = key[ 0] & 0xff | (key[ 1] & 0xff) << 8; this.r[0] = ( t0                     ) & 0x1fff;\n  t1 = key[ 2] & 0xff | (key[ 3] & 0xff) << 8; this.r[1] = ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n  t2 = key[ 4] & 0xff | (key[ 5] & 0xff) << 8; this.r[2] = ((t1 >>> 10) | (t2 <<  6)) & 0x1f03;\n  t3 = key[ 6] & 0xff | (key[ 7] & 0xff) << 8; this.r[3] = ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n  t4 = key[ 8] & 0xff | (key[ 9] & 0xff) << 8; this.r[4] = ((t3 >>>  4) | (t4 << 12)) & 0x00ff;\n  this.r[5] = ((t4 >>>  1)) & 0x1ffe;\n  t5 = key[10] & 0xff | (key[11] & 0xff) << 8; this.r[6] = ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n  t6 = key[12] & 0xff | (key[13] & 0xff) << 8; this.r[7] = ((t5 >>> 11) | (t6 <<  5)) & 0x1f81;\n  t7 = key[14] & 0xff | (key[15] & 0xff) << 8; this.r[8] = ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n  this.r[9] = ((t7 >>>  5)) & 0x007f;\n\n  this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n  this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n  this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n  this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n  this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n  this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n  this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n  this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n};\n\npoly1305.prototype.blocks = function(m, mpos, bytes) {\n  var hibit = this.fin ? 0 : (1 << 11);\n  var t0, t1, t2, t3, t4, t5, t6, t7, c;\n  var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n\n  var h0 = this.h[0],\n      h1 = this.h[1],\n      h2 = this.h[2],\n      h3 = this.h[3],\n      h4 = this.h[4],\n      h5 = this.h[5],\n      h6 = this.h[6],\n      h7 = this.h[7],\n      h8 = this.h[8],\n      h9 = this.h[9];\n\n  var r0 = this.r[0],\n      r1 = this.r[1],\n      r2 = this.r[2],\n      r3 = this.r[3],\n      r4 = this.r[4],\n      r5 = this.r[5],\n      r6 = this.r[6],\n      r7 = this.r[7],\n      r8 = this.r[8],\n      r9 = this.r[9];\n\n  while (bytes >= 16) {\n    t0 = m[mpos+ 0] & 0xff | (m[mpos+ 1] & 0xff) << 8; h0 += ( t0                     ) & 0x1fff;\n    t1 = m[mpos+ 2] & 0xff | (m[mpos+ 3] & 0xff) << 8; h1 += ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n    t2 = m[mpos+ 4] & 0xff | (m[mpos+ 5] & 0xff) << 8; h2 += ((t1 >>> 10) | (t2 <<  6)) & 0x1fff;\n    t3 = m[mpos+ 6] & 0xff | (m[mpos+ 7] & 0xff) << 8; h3 += ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n    t4 = m[mpos+ 8] & 0xff | (m[mpos+ 9] & 0xff) << 8; h4 += ((t3 >>>  4) | (t4 << 12)) & 0x1fff;\n    h5 += ((t4 >>>  1)) & 0x1fff;\n    t5 = m[mpos+10] & 0xff | (m[mpos+11] & 0xff) << 8; h6 += ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n    t6 = m[mpos+12] & 0xff | (m[mpos+13] & 0xff) << 8; h7 += ((t5 >>> 11) | (t6 <<  5)) & 0x1fff;\n    t7 = m[mpos+14] & 0xff | (m[mpos+15] & 0xff) << 8; h8 += ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n    h9 += ((t7 >>> 5)) | hibit;\n\n    c = 0;\n\n    d0 = c;\n    d0 += h0 * r0;\n    d0 += h1 * (5 * r9);\n    d0 += h2 * (5 * r8);\n    d0 += h3 * (5 * r7);\n    d0 += h4 * (5 * r6);\n    c = (d0 >>> 13); d0 &= 0x1fff;\n    d0 += h5 * (5 * r5);\n    d0 += h6 * (5 * r4);\n    d0 += h7 * (5 * r3);\n    d0 += h8 * (5 * r2);\n    d0 += h9 * (5 * r1);\n    c += (d0 >>> 13); d0 &= 0x1fff;\n\n    d1 = c;\n    d1 += h0 * r1;\n    d1 += h1 * r0;\n    d1 += h2 * (5 * r9);\n    d1 += h3 * (5 * r8);\n    d1 += h4 * (5 * r7);\n    c = (d1 >>> 13); d1 &= 0x1fff;\n    d1 += h5 * (5 * r6);\n    d1 += h6 * (5 * r5);\n    d1 += h7 * (5 * r4);\n    d1 += h8 * (5 * r3);\n    d1 += h9 * (5 * r2);\n    c += (d1 >>> 13); d1 &= 0x1fff;\n\n    d2 = c;\n    d2 += h0 * r2;\n    d2 += h1 * r1;\n    d2 += h2 * r0;\n    d2 += h3 * (5 * r9);\n    d2 += h4 * (5 * r8);\n    c = (d2 >>> 13); d2 &= 0x1fff;\n    d2 += h5 * (5 * r7);\n    d2 += h6 * (5 * r6);\n    d2 += h7 * (5 * r5);\n    d2 += h8 * (5 * r4);\n    d2 += h9 * (5 * r3);\n    c += (d2 >>> 13); d2 &= 0x1fff;\n\n    d3 = c;\n    d3 += h0 * r3;\n    d3 += h1 * r2;\n    d3 += h2 * r1;\n    d3 += h3 * r0;\n    d3 += h4 * (5 * r9);\n    c = (d3 >>> 13); d3 &= 0x1fff;\n    d3 += h5 * (5 * r8);\n    d3 += h6 * (5 * r7);\n    d3 += h7 * (5 * r6);\n    d3 += h8 * (5 * r5);\n    d3 += h9 * (5 * r4);\n    c += (d3 >>> 13); d3 &= 0x1fff;\n\n    d4 = c;\n    d4 += h0 * r4;\n    d4 += h1 * r3;\n    d4 += h2 * r2;\n    d4 += h3 * r1;\n    d4 += h4 * r0;\n    c = (d4 >>> 13); d4 &= 0x1fff;\n    d4 += h5 * (5 * r9);\n    d4 += h6 * (5 * r8);\n    d4 += h7 * (5 * r7);\n    d4 += h8 * (5 * r6);\n    d4 += h9 * (5 * r5);\n    c += (d4 >>> 13); d4 &= 0x1fff;\n\n    d5 = c;\n    d5 += h0 * r5;\n    d5 += h1 * r4;\n    d5 += h2 * r3;\n    d5 += h3 * r2;\n    d5 += h4 * r1;\n    c = (d5 >>> 13); d5 &= 0x1fff;\n    d5 += h5 * r0;\n    d5 += h6 * (5 * r9);\n    d5 += h7 * (5 * r8);\n    d5 += h8 * (5 * r7);\n    d5 += h9 * (5 * r6);\n    c += (d5 >>> 13); d5 &= 0x1fff;\n\n    d6 = c;\n    d6 += h0 * r6;\n    d6 += h1 * r5;\n    d6 += h2 * r4;\n    d6 += h3 * r3;\n    d6 += h4 * r2;\n    c = (d6 >>> 13); d6 &= 0x1fff;\n    d6 += h5 * r1;\n    d6 += h6 * r0;\n    d6 += h7 * (5 * r9);\n    d6 += h8 * (5 * r8);\n    d6 += h9 * (5 * r7);\n    c += (d6 >>> 13); d6 &= 0x1fff;\n\n    d7 = c;\n    d7 += h0 * r7;\n    d7 += h1 * r6;\n    d7 += h2 * r5;\n    d7 += h3 * r4;\n    d7 += h4 * r3;\n    c = (d7 >>> 13); d7 &= 0x1fff;\n    d7 += h5 * r2;\n    d7 += h6 * r1;\n    d7 += h7 * r0;\n    d7 += h8 * (5 * r9);\n    d7 += h9 * (5 * r8);\n    c += (d7 >>> 13); d7 &= 0x1fff;\n\n    d8 = c;\n    d8 += h0 * r8;\n    d8 += h1 * r7;\n    d8 += h2 * r6;\n    d8 += h3 * r5;\n    d8 += h4 * r4;\n    c = (d8 >>> 13); d8 &= 0x1fff;\n    d8 += h5 * r3;\n    d8 += h6 * r2;\n    d8 += h7 * r1;\n    d8 += h8 * r0;\n    d8 += h9 * (5 * r9);\n    c += (d8 >>> 13); d8 &= 0x1fff;\n\n    d9 = c;\n    d9 += h0 * r9;\n    d9 += h1 * r8;\n    d9 += h2 * r7;\n    d9 += h3 * r6;\n    d9 += h4 * r5;\n    c = (d9 >>> 13); d9 &= 0x1fff;\n    d9 += h5 * r4;\n    d9 += h6 * r3;\n    d9 += h7 * r2;\n    d9 += h8 * r1;\n    d9 += h9 * r0;\n    c += (d9 >>> 13); d9 &= 0x1fff;\n\n    c = (((c << 2) + c)) | 0;\n    c = (c + d0) | 0;\n    d0 = c & 0x1fff;\n    c = (c >>> 13);\n    d1 += c;\n\n    h0 = d0;\n    h1 = d1;\n    h2 = d2;\n    h3 = d3;\n    h4 = d4;\n    h5 = d5;\n    h6 = d6;\n    h7 = d7;\n    h8 = d8;\n    h9 = d9;\n\n    mpos += 16;\n    bytes -= 16;\n  }\n  this.h[0] = h0;\n  this.h[1] = h1;\n  this.h[2] = h2;\n  this.h[3] = h3;\n  this.h[4] = h4;\n  this.h[5] = h5;\n  this.h[6] = h6;\n  this.h[7] = h7;\n  this.h[8] = h8;\n  this.h[9] = h9;\n};\n\npoly1305.prototype.finish = function(mac, macpos) {\n  var g = new Uint16Array(10);\n  var c, mask, f, i;\n\n  if (this.leftover) {\n    i = this.leftover;\n    this.buffer[i++] = 1;\n    for (; i < 16; i++) this.buffer[i] = 0;\n    this.fin = 1;\n    this.blocks(this.buffer, 0, 16);\n  }\n\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  for (i = 2; i < 10; i++) {\n    this.h[i] += c;\n    c = this.h[i] >>> 13;\n    this.h[i] &= 0x1fff;\n  }\n  this.h[0] += (c * 5);\n  c = this.h[0] >>> 13;\n  this.h[0] &= 0x1fff;\n  this.h[1] += c;\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  this.h[2] += c;\n\n  g[0] = this.h[0] + 5;\n  c = g[0] >>> 13;\n  g[0] &= 0x1fff;\n  for (i = 1; i < 10; i++) {\n    g[i] = this.h[i] + c;\n    c = g[i] >>> 13;\n    g[i] &= 0x1fff;\n  }\n  g[9] -= (1 << 13);\n\n  mask = (c ^ 1) - 1;\n  for (i = 0; i < 10; i++) g[i] &= mask;\n  mask = ~mask;\n  for (i = 0; i < 10; i++) this.h[i] = (this.h[i] & mask) | g[i];\n\n  this.h[0] = ((this.h[0]       ) | (this.h[1] << 13)                    ) & 0xffff;\n  this.h[1] = ((this.h[1] >>>  3) | (this.h[2] << 10)                    ) & 0xffff;\n  this.h[2] = ((this.h[2] >>>  6) | (this.h[3] <<  7)                    ) & 0xffff;\n  this.h[3] = ((this.h[3] >>>  9) | (this.h[4] <<  4)                    ) & 0xffff;\n  this.h[4] = ((this.h[4] >>> 12) | (this.h[5] <<  1) | (this.h[6] << 14)) & 0xffff;\n  this.h[5] = ((this.h[6] >>>  2) | (this.h[7] << 11)                    ) & 0xffff;\n  this.h[6] = ((this.h[7] >>>  5) | (this.h[8] <<  8)                    ) & 0xffff;\n  this.h[7] = ((this.h[8] >>>  8) | (this.h[9] <<  5)                    ) & 0xffff;\n\n  f = this.h[0] + this.pad[0];\n  this.h[0] = f & 0xffff;\n  for (i = 1; i < 8; i++) {\n    f = (((this.h[i] + this.pad[i]) | 0) + (f >>> 16)) | 0;\n    this.h[i] = f & 0xffff;\n  }\n\n  mac[macpos+ 0] = (this.h[0] >>> 0) & 0xff;\n  mac[macpos+ 1] = (this.h[0] >>> 8) & 0xff;\n  mac[macpos+ 2] = (this.h[1] >>> 0) & 0xff;\n  mac[macpos+ 3] = (this.h[1] >>> 8) & 0xff;\n  mac[macpos+ 4] = (this.h[2] >>> 0) & 0xff;\n  mac[macpos+ 5] = (this.h[2] >>> 8) & 0xff;\n  mac[macpos+ 6] = (this.h[3] >>> 0) & 0xff;\n  mac[macpos+ 7] = (this.h[3] >>> 8) & 0xff;\n  mac[macpos+ 8] = (this.h[4] >>> 0) & 0xff;\n  mac[macpos+ 9] = (this.h[4] >>> 8) & 0xff;\n  mac[macpos+10] = (this.h[5] >>> 0) & 0xff;\n  mac[macpos+11] = (this.h[5] >>> 8) & 0xff;\n  mac[macpos+12] = (this.h[6] >>> 0) & 0xff;\n  mac[macpos+13] = (this.h[6] >>> 8) & 0xff;\n  mac[macpos+14] = (this.h[7] >>> 0) & 0xff;\n  mac[macpos+15] = (this.h[7] >>> 8) & 0xff;\n};\n\npoly1305.prototype.update = function(m, mpos, bytes) {\n  var i, want;\n\n  if (this.leftover) {\n    want = (16 - this.leftover);\n    if (want > bytes)\n      want = bytes;\n    for (i = 0; i < want; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    bytes -= want;\n    mpos += want;\n    this.leftover += want;\n    if (this.leftover < 16)\n      return;\n    this.blocks(this.buffer, 0, 16);\n    this.leftover = 0;\n  }\n\n  if (bytes >= 16) {\n    want = bytes - (bytes % 16);\n    this.blocks(m, mpos, want);\n    mpos += want;\n    bytes -= want;\n  }\n\n  if (bytes) {\n    for (i = 0; i < bytes; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    this.leftover += bytes;\n  }\n};\n\nfunction crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n  var s = new poly1305(k);\n  s.update(m, mpos, n);\n  s.finish(out, outpos);\n  return 0;\n}\n\nfunction crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n  var x = new Uint8Array(16);\n  crypto_onetimeauth(x,0,m,mpos,n,k);\n  return crypto_verify_16(h,hpos,x,0);\n}\n\nfunction crypto_secretbox(c,m,d,n,k) {\n  var i;\n  if (d < 32) return -1;\n  crypto_stream_xor(c,0,m,0,d,n,k);\n  crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n  for (i = 0; i < 16; i++) c[i] = 0;\n  return 0;\n}\n\nfunction crypto_secretbox_open(m,c,d,n,k) {\n  var i;\n  var x = new Uint8Array(32);\n  if (d < 32) return -1;\n  crypto_stream(x,0,32,n,k);\n  if (crypto_onetimeauth_verify(c, 16,c, 32,d - 32,x) !== 0) return -1;\n  crypto_stream_xor(m,0,c,0,d,n,k);\n  for (i = 0; i < 32; i++) m[i] = 0;\n  return 0;\n}\n\nfunction set25519(r, a) {\n  var i;\n  for (i = 0; i < 16; i++) r[i] = a[i]|0;\n}\n\nfunction car25519(o) {\n  var i, v, c = 1;\n  for (i = 0; i < 16; i++) {\n    v = o[i] + c + 65535;\n    c = Math.floor(v / 65536);\n    o[i] = v - c * 65536;\n  }\n  o[0] += c-1 + 37 * (c-1);\n}\n\nfunction sel25519(p, q, b) {\n  var t, c = ~(b-1);\n  for (var i = 0; i < 16; i++) {\n    t = c & (p[i] ^ q[i]);\n    p[i] ^= t;\n    q[i] ^= t;\n  }\n}\n\nfunction pack25519(o, n) {\n  var i, j, b;\n  var m = gf(), t = gf();\n  for (i = 0; i < 16; i++) t[i] = n[i];\n  car25519(t);\n  car25519(t);\n  car25519(t);\n  for (j = 0; j < 2; j++) {\n    m[0] = t[0] - 0xffed;\n    for (i = 1; i < 15; i++) {\n      m[i] = t[i] - 0xffff - ((m[i-1]>>16) & 1);\n      m[i-1] &= 0xffff;\n    }\n    m[15] = t[15] - 0x7fff - ((m[14]>>16) & 1);\n    b = (m[15]>>16) & 1;\n    m[14] &= 0xffff;\n    sel25519(t, m, 1-b);\n  }\n  for (i = 0; i < 16; i++) {\n    o[2*i] = t[i] & 0xff;\n    o[2*i+1] = t[i]>>8;\n  }\n}\n\nfunction neq25519(a, b) {\n  var c = new Uint8Array(32), d = new Uint8Array(32);\n  pack25519(c, a);\n  pack25519(d, b);\n  return crypto_verify_32(c, 0, d, 0);\n}\n\nfunction par25519(a) {\n  var d = new Uint8Array(32);\n  pack25519(d, a);\n  return d[0] & 1;\n}\n\nfunction unpack25519(o, n) {\n  var i;\n  for (i = 0; i < 16; i++) o[i] = n[2*i] + (n[2*i+1] << 8);\n  o[15] &= 0x7fff;\n}\n\nfunction A(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] + b[i];\n}\n\nfunction Z(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] - b[i];\n}\n\nfunction M(o, a, b) {\n  var v, c,\n     t0 = 0,  t1 = 0,  t2 = 0,  t3 = 0,  t4 = 0,  t5 = 0,  t6 = 0,  t7 = 0,\n     t8 = 0,  t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0,\n    t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0,\n    t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0,\n    b0 = b[0],\n    b1 = b[1],\n    b2 = b[2],\n    b3 = b[3],\n    b4 = b[4],\n    b5 = b[5],\n    b6 = b[6],\n    b7 = b[7],\n    b8 = b[8],\n    b9 = b[9],\n    b10 = b[10],\n    b11 = b[11],\n    b12 = b[12],\n    b13 = b[13],\n    b14 = b[14],\n    b15 = b[15];\n\n  v = a[0];\n  t0 += v * b0;\n  t1 += v * b1;\n  t2 += v * b2;\n  t3 += v * b3;\n  t4 += v * b4;\n  t5 += v * b5;\n  t6 += v * b6;\n  t7 += v * b7;\n  t8 += v * b8;\n  t9 += v * b9;\n  t10 += v * b10;\n  t11 += v * b11;\n  t12 += v * b12;\n  t13 += v * b13;\n  t14 += v * b14;\n  t15 += v * b15;\n  v = a[1];\n  t1 += v * b0;\n  t2 += v * b1;\n  t3 += v * b2;\n  t4 += v * b3;\n  t5 += v * b4;\n  t6 += v * b5;\n  t7 += v * b6;\n  t8 += v * b7;\n  t9 += v * b8;\n  t10 += v * b9;\n  t11 += v * b10;\n  t12 += v * b11;\n  t13 += v * b12;\n  t14 += v * b13;\n  t15 += v * b14;\n  t16 += v * b15;\n  v = a[2];\n  t2 += v * b0;\n  t3 += v * b1;\n  t4 += v * b2;\n  t5 += v * b3;\n  t6 += v * b4;\n  t7 += v * b5;\n  t8 += v * b6;\n  t9 += v * b7;\n  t10 += v * b8;\n  t11 += v * b9;\n  t12 += v * b10;\n  t13 += v * b11;\n  t14 += v * b12;\n  t15 += v * b13;\n  t16 += v * b14;\n  t17 += v * b15;\n  v = a[3];\n  t3 += v * b0;\n  t4 += v * b1;\n  t5 += v * b2;\n  t6 += v * b3;\n  t7 += v * b4;\n  t8 += v * b5;\n  t9 += v * b6;\n  t10 += v * b7;\n  t11 += v * b8;\n  t12 += v * b9;\n  t13 += v * b10;\n  t14 += v * b11;\n  t15 += v * b12;\n  t16 += v * b13;\n  t17 += v * b14;\n  t18 += v * b15;\n  v = a[4];\n  t4 += v * b0;\n  t5 += v * b1;\n  t6 += v * b2;\n  t7 += v * b3;\n  t8 += v * b4;\n  t9 += v * b5;\n  t10 += v * b6;\n  t11 += v * b7;\n  t12 += v * b8;\n  t13 += v * b9;\n  t14 += v * b10;\n  t15 += v * b11;\n  t16 += v * b12;\n  t17 += v * b13;\n  t18 += v * b14;\n  t19 += v * b15;\n  v = a[5];\n  t5 += v * b0;\n  t6 += v * b1;\n  t7 += v * b2;\n  t8 += v * b3;\n  t9 += v * b4;\n  t10 += v * b5;\n  t11 += v * b6;\n  t12 += v * b7;\n  t13 += v * b8;\n  t14 += v * b9;\n  t15 += v * b10;\n  t16 += v * b11;\n  t17 += v * b12;\n  t18 += v * b13;\n  t19 += v * b14;\n  t20 += v * b15;\n  v = a[6];\n  t6 += v * b0;\n  t7 += v * b1;\n  t8 += v * b2;\n  t9 += v * b3;\n  t10 += v * b4;\n  t11 += v * b5;\n  t12 += v * b6;\n  t13 += v * b7;\n  t14 += v * b8;\n  t15 += v * b9;\n  t16 += v * b10;\n  t17 += v * b11;\n  t18 += v * b12;\n  t19 += v * b13;\n  t20 += v * b14;\n  t21 += v * b15;\n  v = a[7];\n  t7 += v * b0;\n  t8 += v * b1;\n  t9 += v * b2;\n  t10 += v * b3;\n  t11 += v * b4;\n  t12 += v * b5;\n  t13 += v * b6;\n  t14 += v * b7;\n  t15 += v * b8;\n  t16 += v * b9;\n  t17 += v * b10;\n  t18 += v * b11;\n  t19 += v * b12;\n  t20 += v * b13;\n  t21 += v * b14;\n  t22 += v * b15;\n  v = a[8];\n  t8 += v * b0;\n  t9 += v * b1;\n  t10 += v * b2;\n  t11 += v * b3;\n  t12 += v * b4;\n  t13 += v * b5;\n  t14 += v * b6;\n  t15 += v * b7;\n  t16 += v * b8;\n  t17 += v * b9;\n  t18 += v * b10;\n  t19 += v * b11;\n  t20 += v * b12;\n  t21 += v * b13;\n  t22 += v * b14;\n  t23 += v * b15;\n  v = a[9];\n  t9 += v * b0;\n  t10 += v * b1;\n  t11 += v * b2;\n  t12 += v * b3;\n  t13 += v * b4;\n  t14 += v * b5;\n  t15 += v * b6;\n  t16 += v * b7;\n  t17 += v * b8;\n  t18 += v * b9;\n  t19 += v * b10;\n  t20 += v * b11;\n  t21 += v * b12;\n  t22 += v * b13;\n  t23 += v * b14;\n  t24 += v * b15;\n  v = a[10];\n  t10 += v * b0;\n  t11 += v * b1;\n  t12 += v * b2;\n  t13 += v * b3;\n  t14 += v * b4;\n  t15 += v * b5;\n  t16 += v * b6;\n  t17 += v * b7;\n  t18 += v * b8;\n  t19 += v * b9;\n  t20 += v * b10;\n  t21 += v * b11;\n  t22 += v * b12;\n  t23 += v * b13;\n  t24 += v * b14;\n  t25 += v * b15;\n  v = a[11];\n  t11 += v * b0;\n  t12 += v * b1;\n  t13 += v * b2;\n  t14 += v * b3;\n  t15 += v * b4;\n  t16 += v * b5;\n  t17 += v * b6;\n  t18 += v * b7;\n  t19 += v * b8;\n  t20 += v * b9;\n  t21 += v * b10;\n  t22 += v * b11;\n  t23 += v * b12;\n  t24 += v * b13;\n  t25 += v * b14;\n  t26 += v * b15;\n  v = a[12];\n  t12 += v * b0;\n  t13 += v * b1;\n  t14 += v * b2;\n  t15 += v * b3;\n  t16 += v * b4;\n  t17 += v * b5;\n  t18 += v * b6;\n  t19 += v * b7;\n  t20 += v * b8;\n  t21 += v * b9;\n  t22 += v * b10;\n  t23 += v * b11;\n  t24 += v * b12;\n  t25 += v * b13;\n  t26 += v * b14;\n  t27 += v * b15;\n  v = a[13];\n  t13 += v * b0;\n  t14 += v * b1;\n  t15 += v * b2;\n  t16 += v * b3;\n  t17 += v * b4;\n  t18 += v * b5;\n  t19 += v * b6;\n  t20 += v * b7;\n  t21 += v * b8;\n  t22 += v * b9;\n  t23 += v * b10;\n  t24 += v * b11;\n  t25 += v * b12;\n  t26 += v * b13;\n  t27 += v * b14;\n  t28 += v * b15;\n  v = a[14];\n  t14 += v * b0;\n  t15 += v * b1;\n  t16 += v * b2;\n  t17 += v * b3;\n  t18 += v * b4;\n  t19 += v * b5;\n  t20 += v * b6;\n  t21 += v * b7;\n  t22 += v * b8;\n  t23 += v * b9;\n  t24 += v * b10;\n  t25 += v * b11;\n  t26 += v * b12;\n  t27 += v * b13;\n  t28 += v * b14;\n  t29 += v * b15;\n  v = a[15];\n  t15 += v * b0;\n  t16 += v * b1;\n  t17 += v * b2;\n  t18 += v * b3;\n  t19 += v * b4;\n  t20 += v * b5;\n  t21 += v * b6;\n  t22 += v * b7;\n  t23 += v * b8;\n  t24 += v * b9;\n  t25 += v * b10;\n  t26 += v * b11;\n  t27 += v * b12;\n  t28 += v * b13;\n  t29 += v * b14;\n  t30 += v * b15;\n\n  t0  += 38 * t16;\n  t1  += 38 * t17;\n  t2  += 38 * t18;\n  t3  += 38 * t19;\n  t4  += 38 * t20;\n  t5  += 38 * t21;\n  t6  += 38 * t22;\n  t7  += 38 * t23;\n  t8  += 38 * t24;\n  t9  += 38 * t25;\n  t10 += 38 * t26;\n  t11 += 38 * t27;\n  t12 += 38 * t28;\n  t13 += 38 * t29;\n  t14 += 38 * t30;\n  // t15 left as is\n\n  // first car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  // second car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  o[ 0] = t0;\n  o[ 1] = t1;\n  o[ 2] = t2;\n  o[ 3] = t3;\n  o[ 4] = t4;\n  o[ 5] = t5;\n  o[ 6] = t6;\n  o[ 7] = t7;\n  o[ 8] = t8;\n  o[ 9] = t9;\n  o[10] = t10;\n  o[11] = t11;\n  o[12] = t12;\n  o[13] = t13;\n  o[14] = t14;\n  o[15] = t15;\n}\n\nfunction S(o, a) {\n  M(o, a, a);\n}\n\nfunction inv25519(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 253; a >= 0; a--) {\n    S(c, c);\n    if(a !== 2 && a !== 4) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction pow2523(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 250; a >= 0; a--) {\n      S(c, c);\n      if(a !== 1) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction crypto_scalarmult(q, n, p) {\n  var z = new Uint8Array(32);\n  var x = new Float64Array(80), r, i;\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf();\n  for (i = 0; i < 31; i++) z[i] = n[i];\n  z[31]=(n[31]&127)|64;\n  z[0]&=248;\n  unpack25519(x,p);\n  for (i = 0; i < 16; i++) {\n    b[i]=x[i];\n    d[i]=a[i]=c[i]=0;\n  }\n  a[0]=d[0]=1;\n  for (i=254; i>=0; --i) {\n    r=(z[i>>>3]>>>(i&7))&1;\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n    A(e,a,c);\n    Z(a,a,c);\n    A(c,b,d);\n    Z(b,b,d);\n    S(d,e);\n    S(f,a);\n    M(a,c,a);\n    M(c,b,e);\n    A(e,a,c);\n    Z(a,a,c);\n    S(b,a);\n    Z(c,d,f);\n    M(a,c,_121665);\n    A(a,a,d);\n    M(c,c,a);\n    M(a,d,f);\n    M(d,b,x);\n    S(b,e);\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n  }\n  for (i = 0; i < 16; i++) {\n    x[i+16]=a[i];\n    x[i+32]=c[i];\n    x[i+48]=b[i];\n    x[i+64]=d[i];\n  }\n  var x32 = x.subarray(32);\n  var x16 = x.subarray(16);\n  inv25519(x32,x32);\n  M(x16,x16,x32);\n  pack25519(q,x16);\n  return 0;\n}\n\nfunction crypto_scalarmult_base(q, n) {\n  return crypto_scalarmult(q, n, _9);\n}\n\nfunction crypto_box_keypair(y, x) {\n  randombytes(x, 32);\n  return crypto_scalarmult_base(y, x);\n}\n\nfunction crypto_box_beforenm(k, y, x) {\n  var s = new Uint8Array(32);\n  crypto_scalarmult(s, x, y);\n  return crypto_core_hsalsa20(k, _0, s, sigma);\n}\n\nvar crypto_box_afternm = crypto_secretbox;\nvar crypto_box_open_afternm = crypto_secretbox_open;\n\nfunction crypto_box(c, m, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_afternm(c, m, d, n, k);\n}\n\nfunction crypto_box_open(m, c, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_open_afternm(m, c, d, n, k);\n}\n\nvar K = [\n  0x428a2f98, 0xd728ae22, 0x71374491, 0x23ef65cd,\n  0xb5c0fbcf, 0xec4d3b2f, 0xe9b5dba5, 0x8189dbbc,\n  0x3956c25b, 0xf348b538, 0x59f111f1, 0xb605d019,\n  0x923f82a4, 0xaf194f9b, 0xab1c5ed5, 0xda6d8118,\n  0xd807aa98, 0xa3030242, 0x12835b01, 0x45706fbe,\n  0x243185be, 0x4ee4b28c, 0x550c7dc3, 0xd5ffb4e2,\n  0x72be5d74, 0xf27b896f, 0x80deb1fe, 0x3b1696b1,\n  0x9bdc06a7, 0x25c71235, 0xc19bf174, 0xcf692694,\n  0xe49b69c1, 0x9ef14ad2, 0xefbe4786, 0x384f25e3,\n  0x0fc19dc6, 0x8b8cd5b5, 0x240ca1cc, 0x77ac9c65,\n  0x2de92c6f, 0x592b0275, 0x4a7484aa, 0x6ea6e483,\n  0x5cb0a9dc, 0xbd41fbd4, 0x76f988da, 0x831153b5,\n  0x983e5152, 0xee66dfab, 0xa831c66d, 0x2db43210,\n  0xb00327c8, 0x98fb213f, 0xbf597fc7, 0xbeef0ee4,\n  0xc6e00bf3, 0x3da88fc2, 0xd5a79147, 0x930aa725,\n  0x06ca6351, 0xe003826f, 0x14292967, 0x0a0e6e70,\n  0x27b70a85, 0x46d22ffc, 0x2e1b2138, 0x5c26c926,\n  0x4d2c6dfc, 0x5ac42aed, 0x53380d13, 0x9d95b3df,\n  0x650a7354, 0x8baf63de, 0x766a0abb, 0x3c77b2a8,\n  0x81c2c92e, 0x47edaee6, 0x92722c85, 0x1482353b,\n  0xa2bfe8a1, 0x4cf10364, 0xa81a664b, 0xbc423001,\n  0xc24b8b70, 0xd0f89791, 0xc76c51a3, 0x0654be30,\n  0xd192e819, 0xd6ef5218, 0xd6990624, 0x5565a910,\n  0xf40e3585, 0x5771202a, 0x106aa070, 0x32bbd1b8,\n  0x19a4c116, 0xb8d2d0c8, 0x1e376c08, 0x5141ab53,\n  0x2748774c, 0xdf8eeb99, 0x34b0bcb5, 0xe19b48a8,\n  0x391c0cb3, 0xc5c95a63, 0x4ed8aa4a, 0xe3418acb,\n  0x5b9cca4f, 0x7763e373, 0x682e6ff3, 0xd6b2b8a3,\n  0x748f82ee, 0x5defb2fc, 0x78a5636f, 0x43172f60,\n  0x84c87814, 0xa1f0ab72, 0x8cc70208, 0x1a6439ec,\n  0x90befffa, 0x23631e28, 0xa4506ceb, 0xde82bde9,\n  0xbef9a3f7, 0xb2c67915, 0xc67178f2, 0xe372532b,\n  0xca273ece, 0xea26619c, 0xd186b8c7, 0x21c0c207,\n  0xeada7dd6, 0xcde0eb1e, 0xf57d4f7f, 0xee6ed178,\n  0x06f067aa, 0x72176fba, 0x0a637dc5, 0xa2c898a6,\n  0x113f9804, 0xbef90dae, 0x1b710b35, 0x131c471b,\n  0x28db77f5, 0x23047d84, 0x32caab7b, 0x40c72493,\n  0x3c9ebe0a, 0x15c9bebc, 0x431d67c4, 0x9c100d4c,\n  0x4cc5d4be, 0xcb3e42b6, 0x597f299c, 0xfc657e2a,\n  0x5fcb6fab, 0x3ad6faec, 0x6c44198c, 0x4a475817\n];\n\nfunction crypto_hashblocks_hl(hh, hl, m, n) {\n  var wh = new Int32Array(16), wl = new Int32Array(16),\n      bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7,\n      bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7,\n      th, tl, i, j, h, l, a, b, c, d;\n\n  var ah0 = hh[0],\n      ah1 = hh[1],\n      ah2 = hh[2],\n      ah3 = hh[3],\n      ah4 = hh[4],\n      ah5 = hh[5],\n      ah6 = hh[6],\n      ah7 = hh[7],\n\n      al0 = hl[0],\n      al1 = hl[1],\n      al2 = hl[2],\n      al3 = hl[3],\n      al4 = hl[4],\n      al5 = hl[5],\n      al6 = hl[6],\n      al7 = hl[7];\n\n  var pos = 0;\n  while (n >= 128) {\n    for (i = 0; i < 16; i++) {\n      j = 8 * i + pos;\n      wh[i] = (m[j+0] << 24) | (m[j+1] << 16) | (m[j+2] << 8) | m[j+3];\n      wl[i] = (m[j+4] << 24) | (m[j+5] << 16) | (m[j+6] << 8) | m[j+7];\n    }\n    for (i = 0; i < 80; i++) {\n      bh0 = ah0;\n      bh1 = ah1;\n      bh2 = ah2;\n      bh3 = ah3;\n      bh4 = ah4;\n      bh5 = ah5;\n      bh6 = ah6;\n      bh7 = ah7;\n\n      bl0 = al0;\n      bl1 = al1;\n      bl2 = al2;\n      bl3 = al3;\n      bl4 = al4;\n      bl5 = al5;\n      bl6 = al6;\n      bl7 = al7;\n\n      // add\n      h = ah7;\n      l = al7;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma1\n      h = ((ah4 >>> 14) | (al4 << (32-14))) ^ ((ah4 >>> 18) | (al4 << (32-18))) ^ ((al4 >>> (41-32)) | (ah4 << (32-(41-32))));\n      l = ((al4 >>> 14) | (ah4 << (32-14))) ^ ((al4 >>> 18) | (ah4 << (32-18))) ^ ((ah4 >>> (41-32)) | (al4 << (32-(41-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Ch\n      h = (ah4 & ah5) ^ (~ah4 & ah6);\n      l = (al4 & al5) ^ (~al4 & al6);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // K\n      h = K[i*2];\n      l = K[i*2+1];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // w\n      h = wh[i%16];\n      l = wl[i%16];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      th = c & 0xffff | d << 16;\n      tl = a & 0xffff | b << 16;\n\n      // add\n      h = th;\n      l = tl;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma0\n      h = ((ah0 >>> 28) | (al0 << (32-28))) ^ ((al0 >>> (34-32)) | (ah0 << (32-(34-32)))) ^ ((al0 >>> (39-32)) | (ah0 << (32-(39-32))));\n      l = ((al0 >>> 28) | (ah0 << (32-28))) ^ ((ah0 >>> (34-32)) | (al0 << (32-(34-32)))) ^ ((ah0 >>> (39-32)) | (al0 << (32-(39-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Maj\n      h = (ah0 & ah1) ^ (ah0 & ah2) ^ (ah1 & ah2);\n      l = (al0 & al1) ^ (al0 & al2) ^ (al1 & al2);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh7 = (c & 0xffff) | (d << 16);\n      bl7 = (a & 0xffff) | (b << 16);\n\n      // add\n      h = bh3;\n      l = bl3;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      h = th;\n      l = tl;\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh3 = (c & 0xffff) | (d << 16);\n      bl3 = (a & 0xffff) | (b << 16);\n\n      ah1 = bh0;\n      ah2 = bh1;\n      ah3 = bh2;\n      ah4 = bh3;\n      ah5 = bh4;\n      ah6 = bh5;\n      ah7 = bh6;\n      ah0 = bh7;\n\n      al1 = bl0;\n      al2 = bl1;\n      al3 = bl2;\n      al4 = bl3;\n      al5 = bl4;\n      al6 = bl5;\n      al7 = bl6;\n      al0 = bl7;\n\n      if (i%16 === 15) {\n        for (j = 0; j < 16; j++) {\n          // add\n          h = wh[j];\n          l = wl[j];\n\n          a = l & 0xffff; b = l >>> 16;\n          c = h & 0xffff; d = h >>> 16;\n\n          h = wh[(j+9)%16];\n          l = wl[(j+9)%16];\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma0\n          th = wh[(j+1)%16];\n          tl = wl[(j+1)%16];\n          h = ((th >>> 1) | (tl << (32-1))) ^ ((th >>> 8) | (tl << (32-8))) ^ (th >>> 7);\n          l = ((tl >>> 1) | (th << (32-1))) ^ ((tl >>> 8) | (th << (32-8))) ^ ((tl >>> 7) | (th << (32-7)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma1\n          th = wh[(j+14)%16];\n          tl = wl[(j+14)%16];\n          h = ((th >>> 19) | (tl << (32-19))) ^ ((tl >>> (61-32)) | (th << (32-(61-32)))) ^ (th >>> 6);\n          l = ((tl >>> 19) | (th << (32-19))) ^ ((th >>> (61-32)) | (tl << (32-(61-32)))) ^ ((tl >>> 6) | (th << (32-6)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          b += a >>> 16;\n          c += b >>> 16;\n          d += c >>> 16;\n\n          wh[j] = (c & 0xffff) | (d << 16);\n          wl[j] = (a & 0xffff) | (b << 16);\n        }\n      }\n    }\n\n    // add\n    h = ah0;\n    l = al0;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[0];\n    l = hl[0];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[0] = ah0 = (c & 0xffff) | (d << 16);\n    hl[0] = al0 = (a & 0xffff) | (b << 16);\n\n    h = ah1;\n    l = al1;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[1];\n    l = hl[1];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[1] = ah1 = (c & 0xffff) | (d << 16);\n    hl[1] = al1 = (a & 0xffff) | (b << 16);\n\n    h = ah2;\n    l = al2;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[2];\n    l = hl[2];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[2] = ah2 = (c & 0xffff) | (d << 16);\n    hl[2] = al2 = (a & 0xffff) | (b << 16);\n\n    h = ah3;\n    l = al3;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[3];\n    l = hl[3];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[3] = ah3 = (c & 0xffff) | (d << 16);\n    hl[3] = al3 = (a & 0xffff) | (b << 16);\n\n    h = ah4;\n    l = al4;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[4];\n    l = hl[4];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[4] = ah4 = (c & 0xffff) | (d << 16);\n    hl[4] = al4 = (a & 0xffff) | (b << 16);\n\n    h = ah5;\n    l = al5;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[5];\n    l = hl[5];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[5] = ah5 = (c & 0xffff) | (d << 16);\n    hl[5] = al5 = (a & 0xffff) | (b << 16);\n\n    h = ah6;\n    l = al6;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[6];\n    l = hl[6];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[6] = ah6 = (c & 0xffff) | (d << 16);\n    hl[6] = al6 = (a & 0xffff) | (b << 16);\n\n    h = ah7;\n    l = al7;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[7];\n    l = hl[7];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[7] = ah7 = (c & 0xffff) | (d << 16);\n    hl[7] = al7 = (a & 0xffff) | (b << 16);\n\n    pos += 128;\n    n -= 128;\n  }\n\n  return n;\n}\n\nfunction crypto_hash(out, m, n) {\n  var hh = new Int32Array(8),\n      hl = new Int32Array(8),\n      x = new Uint8Array(256),\n      i, b = n;\n\n  hh[0] = 0x6a09e667;\n  hh[1] = 0xbb67ae85;\n  hh[2] = 0x3c6ef372;\n  hh[3] = 0xa54ff53a;\n  hh[4] = 0x510e527f;\n  hh[5] = 0x9b05688c;\n  hh[6] = 0x1f83d9ab;\n  hh[7] = 0x5be0cd19;\n\n  hl[0] = 0xf3bcc908;\n  hl[1] = 0x84caa73b;\n  hl[2] = 0xfe94f82b;\n  hl[3] = 0x5f1d36f1;\n  hl[4] = 0xade682d1;\n  hl[5] = 0x2b3e6c1f;\n  hl[6] = 0xfb41bd6b;\n  hl[7] = 0x137e2179;\n\n  crypto_hashblocks_hl(hh, hl, m, n);\n  n %= 128;\n\n  for (i = 0; i < n; i++) x[i] = m[b-n+i];\n  x[n] = 128;\n\n  n = 256-128*(n<112?1:0);\n  x[n-9] = 0;\n  ts64(x, n-8,  (b / 0x20000000) | 0, b << 3);\n  crypto_hashblocks_hl(hh, hl, x, n);\n\n  for (i = 0; i < 8; i++) ts64(out, 8*i, hh[i], hl[i]);\n\n  return 0;\n}\n\nfunction add(p, q) {\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf(),\n      g = gf(), h = gf(), t = gf();\n\n  Z(a, p[1], p[0]);\n  Z(t, q[1], q[0]);\n  M(a, a, t);\n  A(b, p[0], p[1]);\n  A(t, q[0], q[1]);\n  M(b, b, t);\n  M(c, p[3], q[3]);\n  M(c, c, D2);\n  M(d, p[2], q[2]);\n  A(d, d, d);\n  Z(e, b, a);\n  Z(f, d, c);\n  A(g, d, c);\n  A(h, b, a);\n\n  M(p[0], e, f);\n  M(p[1], h, g);\n  M(p[2], g, f);\n  M(p[3], e, h);\n}\n\nfunction cswap(p, q, b) {\n  var i;\n  for (i = 0; i < 4; i++) {\n    sel25519(p[i], q[i], b);\n  }\n}\n\nfunction pack(r, p) {\n  var tx = gf(), ty = gf(), zi = gf();\n  inv25519(zi, p[2]);\n  M(tx, p[0], zi);\n  M(ty, p[1], zi);\n  pack25519(r, ty);\n  r[31] ^= par25519(tx) << 7;\n}\n\nfunction scalarmult(p, q, s) {\n  var b, i;\n  set25519(p[0], gf0);\n  set25519(p[1], gf1);\n  set25519(p[2], gf1);\n  set25519(p[3], gf0);\n  for (i = 255; i >= 0; --i) {\n    b = (s[(i/8)|0] >> (i&7)) & 1;\n    cswap(p, q, b);\n    add(q, p);\n    add(p, p);\n    cswap(p, q, b);\n  }\n}\n\nfunction scalarbase(p, s) {\n  var q = [gf(), gf(), gf(), gf()];\n  set25519(q[0], X);\n  set25519(q[1], Y);\n  set25519(q[2], gf1);\n  M(q[3], X, Y);\n  scalarmult(p, q, s);\n}\n\nfunction crypto_sign_keypair(pk, sk, seeded) {\n  var d = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n  var i;\n\n  if (!seeded) randombytes(sk, 32);\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  scalarbase(p, d);\n  pack(pk, p);\n\n  for (i = 0; i < 32; i++) sk[i+32] = pk[i];\n  return 0;\n}\n\nvar L = new Float64Array([0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0x10]);\n\nfunction modL(r, x) {\n  var carry, i, j, k;\n  for (i = 63; i >= 32; --i) {\n    carry = 0;\n    for (j = i - 32, k = i - 12; j < k; ++j) {\n      x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n      carry = Math.floor((x[j] + 128) / 256);\n      x[j] -= carry * 256;\n    }\n    x[j] += carry;\n    x[i] = 0;\n  }\n  carry = 0;\n  for (j = 0; j < 32; j++) {\n    x[j] += carry - (x[31] >> 4) * L[j];\n    carry = x[j] >> 8;\n    x[j] &= 255;\n  }\n  for (j = 0; j < 32; j++) x[j] -= carry * L[j];\n  for (i = 0; i < 32; i++) {\n    x[i+1] += x[i] >> 8;\n    r[i] = x[i] & 255;\n  }\n}\n\nfunction reduce(r) {\n  var x = new Float64Array(64), i;\n  for (i = 0; i < 64; i++) x[i] = r[i];\n  for (i = 0; i < 64; i++) r[i] = 0;\n  modL(r, x);\n}\n\n// Note: difference from C - smlen returned, not passed as argument.\nfunction crypto_sign(sm, m, n, sk) {\n  var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);\n  var i, j, x = new Float64Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  var smlen = n + 64;\n  for (i = 0; i < n; i++) sm[64 + i] = m[i];\n  for (i = 0; i < 32; i++) sm[32 + i] = d[32 + i];\n\n  crypto_hash(r, sm.subarray(32), n+32);\n  reduce(r);\n  scalarbase(p, r);\n  pack(sm, p);\n\n  for (i = 32; i < 64; i++) sm[i] = sk[i];\n  crypto_hash(h, sm, n + 64);\n  reduce(h);\n\n  for (i = 0; i < 64; i++) x[i] = 0;\n  for (i = 0; i < 32; i++) x[i] = r[i];\n  for (i = 0; i < 32; i++) {\n    for (j = 0; j < 32; j++) {\n      x[i+j] += h[i] * d[j];\n    }\n  }\n\n  modL(sm.subarray(32), x);\n  return smlen;\n}\n\nfunction unpackneg(r, p) {\n  var t = gf(), chk = gf(), num = gf(),\n      den = gf(), den2 = gf(), den4 = gf(),\n      den6 = gf();\n\n  set25519(r[2], gf1);\n  unpack25519(r[1], p);\n  S(num, r[1]);\n  M(den, num, D);\n  Z(num, num, r[2]);\n  A(den, r[2], den);\n\n  S(den2, den);\n  S(den4, den2);\n  M(den6, den4, den2);\n  M(t, den6, num);\n  M(t, t, den);\n\n  pow2523(t, t);\n  M(t, t, num);\n  M(t, t, den);\n  M(t, t, den);\n  M(r[0], t, den);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) M(r[0], r[0], I);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) return -1;\n\n  if (par25519(r[0]) === (p[31]>>7)) Z(r[0], gf0, r[0]);\n\n  M(r[3], r[0], r[1]);\n  return 0;\n}\n\nfunction crypto_sign_open(m, sm, n, pk) {\n  var i;\n  var t = new Uint8Array(32), h = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()],\n      q = [gf(), gf(), gf(), gf()];\n\n  if (n < 64) return -1;\n\n  if (unpackneg(q, pk)) return -1;\n\n  for (i = 0; i < n; i++) m[i] = sm[i];\n  for (i = 0; i < 32; i++) m[i+32] = pk[i];\n  crypto_hash(h, m, n);\n  reduce(h);\n  scalarmult(p, q, h);\n\n  scalarbase(q, sm.subarray(32));\n  add(p, q);\n  pack(t, p);\n\n  n -= 64;\n  if (crypto_verify_32(sm, 0, t, 0)) {\n    for (i = 0; i < n; i++) m[i] = 0;\n    return -1;\n  }\n\n  for (i = 0; i < n; i++) m[i] = sm[i + 64];\n  return n;\n}\n\nvar crypto_secretbox_KEYBYTES = 32,\n    crypto_secretbox_NONCEBYTES = 24,\n    crypto_secretbox_ZEROBYTES = 32,\n    crypto_secretbox_BOXZEROBYTES = 16,\n    crypto_scalarmult_BYTES = 32,\n    crypto_scalarmult_SCALARBYTES = 32,\n    crypto_box_PUBLICKEYBYTES = 32,\n    crypto_box_SECRETKEYBYTES = 32,\n    crypto_box_BEFORENMBYTES = 32,\n    crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES,\n    crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES,\n    crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES,\n    crypto_sign_BYTES = 64,\n    crypto_sign_PUBLICKEYBYTES = 32,\n    crypto_sign_SECRETKEYBYTES = 64,\n    crypto_sign_SEEDBYTES = 32,\n    crypto_hash_BYTES = 64;\n\nnacl.lowlevel = {\n  crypto_core_hsalsa20: crypto_core_hsalsa20,\n  crypto_stream_xor: crypto_stream_xor,\n  crypto_stream: crypto_stream,\n  crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n  crypto_stream_salsa20: crypto_stream_salsa20,\n  crypto_onetimeauth: crypto_onetimeauth,\n  crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n  crypto_verify_16: crypto_verify_16,\n  crypto_verify_32: crypto_verify_32,\n  crypto_secretbox: crypto_secretbox,\n  crypto_secretbox_open: crypto_secretbox_open,\n  crypto_scalarmult: crypto_scalarmult,\n  crypto_scalarmult_base: crypto_scalarmult_base,\n  crypto_box_beforenm: crypto_box_beforenm,\n  crypto_box_afternm: crypto_box_afternm,\n  crypto_box: crypto_box,\n  crypto_box_open: crypto_box_open,\n  crypto_box_keypair: crypto_box_keypair,\n  crypto_hash: crypto_hash,\n  crypto_sign: crypto_sign,\n  crypto_sign_keypair: crypto_sign_keypair,\n  crypto_sign_open: crypto_sign_open,\n\n  crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n  crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n  crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n  crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n  crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n  crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n  crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n  crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n  crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n  crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n  crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n  crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n  crypto_sign_BYTES: crypto_sign_BYTES,\n  crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n  crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n  crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n  crypto_hash_BYTES: crypto_hash_BYTES,\n\n  gf: gf,\n  D: D,\n  L: L,\n  pack25519: pack25519,\n  unpack25519: unpack25519,\n  M: M,\n  A: A,\n  S: S,\n  Z: Z,\n  pow2523: pow2523,\n  add: add,\n  set25519: set25519,\n  modL: modL,\n  scalarmult: scalarmult,\n  scalarbase: scalarbase,\n};\n\n/* High-level API */\n\nfunction checkLengths(k, n) {\n  if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');\n  if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');\n}\n\nfunction checkBoxLengths(pk, sk) {\n  if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');\n  if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n}\n\nfunction checkArrayTypes() {\n  for (var i = 0; i < arguments.length; i++) {\n    if (!(arguments[i] instanceof Uint8Array))\n      throw new TypeError('unexpected type, use Uint8Array');\n  }\n}\n\nfunction cleanup(arr) {\n  for (var i = 0; i < arr.length; i++) arr[i] = 0;\n}\n\nnacl.randomBytes = function(n) {\n  var b = new Uint8Array(n);\n  randombytes(b, n);\n  return b;\n};\n\nnacl.secretbox = function(msg, nonce, key) {\n  checkArrayTypes(msg, nonce, key);\n  checkLengths(key, nonce);\n  var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n  var c = new Uint8Array(m.length);\n  for (var i = 0; i < msg.length; i++) m[i+crypto_secretbox_ZEROBYTES] = msg[i];\n  crypto_secretbox(c, m, m.length, nonce, key);\n  return c.subarray(crypto_secretbox_BOXZEROBYTES);\n};\n\nnacl.secretbox.open = function(box, nonce, key) {\n  checkArrayTypes(box, nonce, key);\n  checkLengths(key, nonce);\n  var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n  var m = new Uint8Array(c.length);\n  for (var i = 0; i < box.length; i++) c[i+crypto_secretbox_BOXZEROBYTES] = box[i];\n  if (c.length < 32) return null;\n  if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n  return m.subarray(crypto_secretbox_ZEROBYTES);\n};\n\nnacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\nnacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\nnacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n\nnacl.scalarMult = function(n, p) {\n  checkArrayTypes(n, p);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult(q, n, p);\n  return q;\n};\n\nnacl.scalarMult.base = function(n) {\n  checkArrayTypes(n);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult_base(q, n);\n  return q;\n};\n\nnacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\nnacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n\nnacl.box = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox(msg, nonce, k);\n};\n\nnacl.box.before = function(publicKey, secretKey) {\n  checkArrayTypes(publicKey, secretKey);\n  checkBoxLengths(publicKey, secretKey);\n  var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n  crypto_box_beforenm(k, publicKey, secretKey);\n  return k;\n};\n\nnacl.box.after = nacl.secretbox;\n\nnacl.box.open = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox.open(msg, nonce, k);\n};\n\nnacl.box.open.after = nacl.secretbox.open;\n\nnacl.box.keyPair = function() {\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n  crypto_box_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.box.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_box_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  crypto_scalarmult_base(pk, secretKey);\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\nnacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\nnacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\nnacl.box.nonceLength = crypto_box_NONCEBYTES;\nnacl.box.overheadLength = nacl.secretbox.overheadLength;\n\nnacl.sign = function(msg, secretKey) {\n  checkArrayTypes(msg, secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var signedMsg = new Uint8Array(crypto_sign_BYTES+msg.length);\n  crypto_sign(signedMsg, msg, msg.length, secretKey);\n  return signedMsg;\n};\n\nnacl.sign.open = function(signedMsg, publicKey) {\n  checkArrayTypes(signedMsg, publicKey);\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var tmp = new Uint8Array(signedMsg.length);\n  var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n  if (mlen < 0) return null;\n  var m = new Uint8Array(mlen);\n  for (var i = 0; i < m.length; i++) m[i] = tmp[i];\n  return m;\n};\n\nnacl.sign.detached = function(msg, secretKey) {\n  var signedMsg = nacl.sign(msg, secretKey);\n  var sig = new Uint8Array(crypto_sign_BYTES);\n  for (var i = 0; i < sig.length; i++) sig[i] = signedMsg[i];\n  return sig;\n};\n\nnacl.sign.detached.verify = function(msg, sig, publicKey) {\n  checkArrayTypes(msg, sig, publicKey);\n  if (sig.length !== crypto_sign_BYTES)\n    throw new Error('bad signature size');\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var i;\n  for (i = 0; i < crypto_sign_BYTES; i++) sm[i] = sig[i];\n  for (i = 0; i < msg.length; i++) sm[i+crypto_sign_BYTES] = msg[i];\n  return (crypto_sign_open(m, sm, sm.length, publicKey) >= 0);\n};\n\nnacl.sign.keyPair = function() {\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  crypto_sign_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  for (var i = 0; i < pk.length; i++) pk[i] = secretKey[32+i];\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.sign.keyPair.fromSeed = function(seed) {\n  checkArrayTypes(seed);\n  if (seed.length !== crypto_sign_SEEDBYTES)\n    throw new Error('bad seed size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  for (var i = 0; i < 32; i++) sk[i] = seed[i];\n  crypto_sign_keypair(pk, sk, true);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\nnacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\nnacl.sign.seedLength = crypto_sign_SEEDBYTES;\nnacl.sign.signatureLength = crypto_sign_BYTES;\n\nnacl.hash = function(msg) {\n  checkArrayTypes(msg);\n  var h = new Uint8Array(crypto_hash_BYTES);\n  crypto_hash(h, msg, msg.length);\n  return h;\n};\n\nnacl.hash.hashLength = crypto_hash_BYTES;\n\nnacl.verify = function(x, y) {\n  checkArrayTypes(x, y);\n  // Zero length arguments are considered not equal.\n  if (x.length === 0 || y.length === 0) return false;\n  if (x.length !== y.length) return false;\n  return (vn(x, 0, y, 0, x.length) === 0) ? true : false;\n};\n\nnacl.setPRNG = function(fn) {\n  randombytes = fn;\n};\n\n(function() {\n  // Initialize PRNG if environment provides CSPRNG.\n  // If not, methods calling randombytes will throw.\n  var crypto = typeof self !== 'undefined' ? (self.crypto || self.msCrypto) : null;\n  if (crypto && crypto.getRandomValues) {\n    // Browsers.\n    var QUOTA = 65536;\n    nacl.setPRNG(function(x, n) {\n      var i, v = new Uint8Array(n);\n      for (i = 0; i < n; i += QUOTA) {\n        crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n      }\n      for (i = 0; i < n; i++) x[i] = v[i];\n      cleanup(v);\n    });\n  } else if (true) {\n    // Node.js.\n    crypto = __webpack_require__(/*! crypto */ \"crypto\");\n    if (crypto && crypto.randomBytes) {\n      nacl.setPRNG(function(x, n) {\n        var i, v = crypto.randomBytes(n);\n        for (i = 0; i < n; i++) x[i] = v[i];\n        cleanup(v);\n      });\n    }\n  }\n})();\n\n})( true && module.exports ? module.exports : (self.nacl = self.nacl || {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tweetnacl/nacl-fast.js\n");

/***/ })

};
;