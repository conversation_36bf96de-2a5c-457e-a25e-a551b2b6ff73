/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tweetnacl-util";
exports.ids = ["vendor-chunks/tweetnacl-util"];
exports.modules = {

/***/ "(ssr)/./node_modules/tweetnacl-util/nacl-util.js":
/*!**************************************************!*\
  !*** ./node_modules/tweetnacl-util/nacl-util.js ***!
  \**************************************************/
/***/ (function(module) {

eval("// Written in 2014-2016 by Dmitry Chestnykh and Devi Mandiri.\n// Public domain.\n(function(root, f) {\n  'use strict';\n  if ( true && module.exports) module.exports = f();\n  else if (root.nacl) root.nacl.util = f();\n  else {\n    root.nacl = {};\n    root.nacl.util = f();\n  }\n}(this, function() {\n  'use strict';\n\n  var util = {};\n\n  function validateBase64(s) {\n    if (!(/^(?:[A-Za-z0-9+\\/]{2}[A-Za-z0-9+\\/]{2})*(?:[A-Za-z0-9+\\/]{2}==|[A-Za-z0-9+\\/]{3}=)?$/.test(s))) {\n      throw new TypeError('invalid encoding');\n    }\n  }\n\n  util.decodeUTF8 = function(s) {\n    if (typeof s !== 'string') throw new TypeError('expected string');\n    var i, d = unescape(encodeURIComponent(s)), b = new Uint8Array(d.length);\n    for (i = 0; i < d.length; i++) b[i] = d.charCodeAt(i);\n    return b;\n  };\n\n  util.encodeUTF8 = function(arr) {\n    var i, s = [];\n    for (i = 0; i < arr.length; i++) s.push(String.fromCharCode(arr[i]));\n    return decodeURIComponent(escape(s.join('')));\n  };\n\n  if (typeof atob === 'undefined') {\n    // Node.js\n\n    if (typeof Buffer.from !== 'undefined') {\n       // Node v6 and later\n      util.encodeBase64 = function (arr) { // v6 and later\n          return Buffer.from(arr).toString('base64');\n      };\n\n      util.decodeBase64 = function (s) {\n        validateBase64(s);\n        return new Uint8Array(Array.prototype.slice.call(Buffer.from(s, 'base64'), 0));\n      };\n\n    } else {\n      // Node earlier than v6\n      util.encodeBase64 = function (arr) { // v6 and later\n        return (new Buffer(arr)).toString('base64');\n      };\n\n      util.decodeBase64 = function(s) {\n        validateBase64(s);\n        return new Uint8Array(Array.prototype.slice.call(new Buffer(s, 'base64'), 0));\n      };\n    }\n\n  } else {\n    // Browsers\n\n    util.encodeBase64 = function(arr) {\n      var i, s = [], len = arr.length;\n      for (i = 0; i < len; i++) s.push(String.fromCharCode(arr[i]));\n      return btoa(s.join(''));\n    };\n\n    util.decodeBase64 = function(s) {\n      validateBase64(s);\n      var i, d = atob(s), b = new Uint8Array(d.length);\n      for (i = 0; i < d.length; i++) b[i] = d.charCodeAt(i);\n      return b;\n    };\n\n  }\n\n  return util;\n\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tweetnacl-util/nacl-util.js\n");

/***/ })

};
;