(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},5511:e=>{"use strict";e.exports=require("crypto")},4735:e=>{"use strict";e.exports=require("events")},1630:e=>{"use strict";e.exports=require("http")},5591:e=>{"use strict";e.exports=require("https")},3873:e=>{"use strict";e.exports=require("path")},1997:e=>{"use strict";e.exports=require("punycode")},7910:e=>{"use strict";e.exports=require("stream")},9551:e=>{"use strict";e.exports=require("url")},8354:e=>{"use strict";e.exports=require("util")},4075:e=>{"use strict";e.exports=require("zlib")},2090:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var r=s(260),n=s(8203),i=s(5155),o=s.n(i),a=s(7292),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);s.d(t,d);let l=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.t.bind(s,9937,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,1354)),"/Users/<USER>/code/tbook/onton_cc/onion-launch/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,1485,23)),"next/dist/client/components/unauthorized-error"]}],c=[],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2390:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,3219,23)),Promise.resolve().then(s.t.bind(s,4863,23)),Promise.resolve().then(s.t.bind(s,5155,23)),Promise.resolve().then(s.t.bind(s,802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,8530,23)),Promise.resolve().then(s.t.bind(s,8921,23))},7662:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6959,23)),Promise.resolve().then(s.t.bind(s,3875,23)),Promise.resolve().then(s.t.bind(s,8903,23)),Promise.resolve().then(s.t.bind(s,7174,23)),Promise.resolve().then(s.t.bind(s,4178,23)),Promise.resolve().then(s.t.bind(s,7190,23)),Promise.resolve().then(s.t.bind(s,1365,23))},8971:(e,t,s)=>{Promise.resolve().then(s.bind(s,4898))},4995:(e,t,s)=>{Promise.resolve().then(s.bind(s,2558))},2558:(e,t,s)=>{"use strict";s.d(t,{Header:()=>a});var r=s(5512),n=s(2299),i=s(4977),o=s(832);function a(){let e=(0,n.c5)();return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83E\uDDC5"}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"ONION"})]}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-6 ml-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{children:"Live Auction"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[(0,r.jsx)(i.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"247 Participants"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Round 12"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,r.jsxs)("div",{className:"hidden md:block text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"font-medium",children:"Connected:"}),(0,r.jsxs)("span",{className:"ml-2 font-mono",children:[e.account.address.slice(0,6),"...",e.account.address.slice(-4)]})]}),(0,r.jsx)(n.MJ,{})]})]})})})}},1354:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>d});var r=s(2740),n=s(7339),i=s.n(n);s(1135);var o=s(8511),a=s(4898);let d={title:"ONION Token Fair Launch",description:"Fair launch platform for ONION token with English auction mechanism"};function l({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:(0,r.jsxs)(o.Nl,{manifestUrl:"https://raw.githubusercontent.com/ton-community/tutorials/main/03-client/test/public/tonconnect-manifest.json",children:[(0,r.jsx)(a.Header,{}),(0,r.jsx)("main",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100",children:e})]})})})}},4898:(e,t,s)=>{"use strict";s.d(t,{Header:()=>r});let r=(0,s(6760).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/code/tbook/onton_cc/onion-launch/frontend/src/components/Header.tsx","Header")},1135:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[6],()=>s(2090));module.exports=r})();