(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{6443:()=>{},1281:()=>{},9797:(e,s,t)=>{Promise.resolve().then(t.bind(t,2995))},2995:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>I});var a=t(5155),r=t(8617),n=t(2423),l=t(6878),i=t(6889),c=t(9191),d=t(2725),o=t(3239),x=t(5340),m=t(2115),u=t(2651);let h=t(2818).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api",g=u.A.create({baseURL:h,timeout:1e4,headers:{"Content-Type":"application/json"}});class N{static async getAuctionState(){try{return(await g.get("/auction/state")).data}catch(e){throw console.error("Failed to get auction state:",e),Error("Failed to fetch auction state")}}static async calculatePurchase(e){try{return(await g.post("/purchase/calculate",e)).data}catch(e){var s,t;if(console.error("Failed to calculate purchase:",e),u.A.isAxiosError(e)&&(null===(t=e.response)||void 0===t?void 0:null===(s=t.data)||void 0===s?void 0:s.error))throw Error(e.response.data.error);throw Error("Failed to calculate purchase")}}static async getPublicKey(){try{return(await g.get("/config/public-key")).data}catch(e){throw console.error("Failed to get public key:",e),Error("Failed to fetch public key")}}static async verifySignature(e,s){try{return(await g.post("/signature/verify",{calculation:e,signature:s})).data}catch(e){throw console.error("Failed to verify signature:",e),Error("Failed to verify signature")}}static async healthCheck(){try{return(await g.get("/health")).data}catch(e){throw console.error("Health check failed:",e),Error("Service unavailable")}}}let j=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9;return(parseFloat(e)/Math.pow(10,s)).toLocaleString(void 0,{maximumFractionDigits:2})},p=e=>e instanceof Error?e.message:"An unexpected error occurred",f={NOT_STARTED:0,ACTIVE:1,PAUSED_BETWEEN_ROUNDS:2,ENDED_SUCCESS:3,ENDED_FAILED:4};function b(e){let s,{data:t}=e,m=e=>new Date(1e3*e).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",timeZone:"UTC",timeZoneName:"short"}),u=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e);return(0,a.jsxs)("div",{className:"bg-white rounded-2xl card-shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg",children:(0,a.jsx)(r.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Auction Information"})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center space-x-2",children:[(0,a.jsx)(n.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Timeline"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Start Time:"}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:t?m(t.startTime):"2025-07-15 00:00 UTC"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"End Time:"}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:t?m(t.endTime):"2025-07-18 00:00 UTC"})]})]}),(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center space-x-2 pt-4",children:[(0,a.jsx)(l.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Caps & Supply"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Soft Cap:"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:u(t?t.softCap:5e5)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Hard Cap:"}),(0,a.jsx)("span",{className:"font-medium text-red-600",children:u(t?t.hardCap:2e6)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Supply:"}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:(s=t?t.totalSupply:1e6,new Intl.NumberFormat("en-US",{minimumFractionDigits:0,maximumFractionDigits:0}).format(s)+" ONION")})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center space-x-2",children:[(0,a.jsx)(i.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:(null==t?void 0:t.auctionStatus)===f.NOT_STARTED?"Starting Price":"Current Price"})]}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-onion-50 to-onion-100 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-onion-700 mb-2",children:u(t?t.currentPrice:.11)}),(0,a.jsx)("div",{className:"text-sm text-onion-600",children:"per ONION token"}),t&&t.currentRound>1&&t.lastRoundPrice&&(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-onion-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Last Round:"}),(0,a.jsx)("span",{className:"font-medium text-gray-700",children:u(t.lastRoundPrice)}),(0,a.jsxs)("span",{className:"font-semibold ".concat(t.currentPrice>t.lastRoundPrice?"text-green-600":t.currentPrice<t.lastRoundPrice?"text-red-600":"text-gray-600"),children:[t.currentPrice>t.lastRoundPrice?"↗":t.currentPrice<t.lastRoundPrice?"↘":"→"," ",((t.currentPrice-t.lastRoundPrice)/t.lastRoundPrice*100).toFixed(1),"%"]})]})})]})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 pt-4",children:"Auction Rules"}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-1.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"English Auction:"})," Price increases with time and demand"]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-1.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Fair & Transparent:"})," Equal opportunity for all participants"]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full mt-1.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Refund Available:"})," Request refund during auction (5% fee)"]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full mt-1.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Minimum Purchase:"})," 50 TON minimum investment"]})]})]})]})]}),t&&(0,a.jsx)("div",{className:"mt-6 p-4 rounded-lg border ".concat(t.auctionStatus===f.NOT_STARTED?"bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200":t.auctionStatus===f.ACTIVE?"bg-gradient-to-r from-green-50 to-green-100 border-green-200":t.auctionStatus===f.PAUSED_BETWEEN_ROUNDS?"bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200":t.auctionStatus===f.ENDED_SUCCESS?"bg-gradient-to-r from-green-50 to-green-100 border-green-200":t.auctionStatus===f.ENDED_FAILED?"bg-gradient-to-r from-red-50 to-red-100 border-red-200":"bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[t.auctionStatus===f.NOT_STARTED&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A,{className:"w-5 h-5 text-blue-600"}),(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Auction has not started yet"})]}),t.auctionStatus===f.ACTIVE&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),(0,a.jsx)(c.A,{className:"w-4 h-4 text-green-600"}),(0,a.jsx)("span",{className:"font-medium text-green-800",children:"Auction is currently ACTIVE"})]}),t.auctionStatus===f.PAUSED_BETWEEN_ROUNDS&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-yellow-600"}),(0,a.jsx)("span",{className:"font-medium text-yellow-800",children:"Calculating next round price"})]}),t.auctionStatus===f.ENDED_SUCCESS&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("span",{className:"font-medium text-green-800",children:"Launch successful!"})]}),t.auctionStatus===f.ENDED_FAILED&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{className:"font-medium text-red-800",children:"Launch failed"})]})]}),(0,a.jsx)("span",{className:"text-sm ".concat(t.auctionStatus===f.NOT_STARTED?"text-blue-600":t.auctionStatus===f.ACTIVE?"text-green-600":t.auctionStatus===f.PAUSED_BETWEEN_ROUNDS?"text-yellow-600":t.auctionStatus===f.ENDED_SUCCESS?"text-green-600":t.auctionStatus===f.ENDED_FAILED?"text-red-600":"text-gray-600"),children:t.auctionStatus===f.NOT_STARTED?"Prepare to participate":t.auctionStatus===f.ACTIVE?"Accepting purchases now":t.auctionStatus===f.PAUSED_BETWEEN_ROUNDS?"Please wait...":t.auctionStatus===f.ENDED_SUCCESS?"Soft cap reached":t.auctionStatus===f.ENDED_FAILED?"Soft cap not reached":"Status unknown"})]})})]})}var y=t(4807),v=t(9676),w=t(1649);function S(e){let{data:s}=e,[t,r]=(0,m.useState)(""),[n,u]=(0,m.useState)("Time Left");(0,m.useEffect)(()=>{if(!s)return;let e=setInterval(()=>{let e=Date.now();switch(s.auctionStatus){case f.NOT_STARTED:let t=1e3*s.startTime;e>=t?(r("Starting..."),u("Status")):(r((0,w.m)(t)),u("Starts In"));break;case f.ACTIVE:let a=1e3*(s.roundEndTime||s.endTime);e>=a?(r("Round Ending..."),u("Status")):(r((0,w.m)(a)),u("Round Ends In"));break;case f.PAUSED_BETWEEN_ROUNDS:let n=1e3*s.nextRoundStartTime;e>=n?(r("Starting Next Round..."),u("Status")):(r((0,w.m)(n)),u("Next Round In"));break;case f.ENDED_SUCCESS:case f.ENDED_FAILED:r("Ended"),u("Status");break;default:let l=1e3*s.endTime;e>=l?(r("Auction Ended"),u("Status")):(r((0,w.m)(l)),u("Time Left"))}},1e3);return()=>clearInterval(e)},[s]);let h=s?s.totalRaised/s.hardCap*100:0,g=s?s.totalTokensSold/s.totalSupply*100:0,N=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),j=e=>new Intl.NumberFormat("en-US",{minimumFractionDigits:0,maximumFractionDigits:0}).format(e)+" ONION",p=(()=>{if(!s)return{color:"gray",text:"Loading...",icon:i.A};switch(s.auctionStatus){case f.NOT_STARTED:return{color:"blue",text:"Not Started",icon:i.A};case f.ACTIVE:return{color:"green",text:"Active",icon:c.A};case f.PAUSED_BETWEEN_ROUNDS:return{color:"yellow",text:"Calculating Next Round",icon:d.A};case f.ENDED_SUCCESS:return{color:"green",text:"Launch Successful",icon:o.A};case f.ENDED_FAILED:return{color:"red",text:"Launch Failed",icon:x.A};default:return{color:"gray",text:"Unknown",icon:i.A}}})();return(0,a.jsxs)("div",{className:"bg-white rounded-2xl card-shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Live Auction Stats"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-".concat(p.color,"-500 rounded-full ").concat((null==s?void 0:s.auctionStatus)===f.ACTIVE?"animate-pulse":"")}),(0,a.jsx)(p.icon,{className:"w-4 h-4 text-".concat(p.color,"-600")}),(0,a.jsx)("span",{className:"text-sm font-medium text-".concat(p.color,"-600"),children:p.text})]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl mb-3 mx-auto",children:(0,a.jsx)(y.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:N(s?s.totalRaised:16e5)}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Raised"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl mb-3 mx-auto",children:(0,a.jsx)(v.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:j(s?s.totalTokensSold:673e3)}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Tokens Sold"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl mb-3 mx-auto",children:(0,a.jsx)(l.A,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:["Round ",(null==s?void 0:s.currentRound)||12]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Current Round"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-".concat(p.color,"-100 rounded-xl mb-3 mx-auto"),children:(0,a.jsx)(i.A,{className:"w-6 h-6 text-".concat(p.color,"-600")})}),(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:t||"1d 12h"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:n})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Funding Progress"}),(0,a.jsxs)("span",{className:"text-sm font-bold ".concat((null==s?void 0:s.auctionStatus)===f.ENDED_SUCCESS?"text-blue-600":(null==s?void 0:s.auctionStatus)===f.ENDED_FAILED?"text-red-600":"text-gray-900"),children:[h.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"h-3 rounded-full transition-all duration-500 ease-out ".concat((null==s?void 0:s.auctionStatus)===f.ENDED_SUCCESS?"bg-blue-500":(null==s?void 0:s.auctionStatus)===f.ENDED_FAILED?"bg-red-500":"progress-bar"),style:{width:"".concat(Math.min(h,100),"%")}})}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,a.jsxs)("span",{children:["Soft Cap: ",N(s?s.softCap:5e5)]}),(0,a.jsxs)("span",{children:["Hard Cap: ",N(s?s.hardCap:2e6)]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Token Distribution"}),(0,a.jsxs)("span",{className:"text-sm font-bold text-gray-900",children:[g.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-onion-500 to-onion-600 h-3 rounded-full transition-all duration-500 ease-out",style:{width:"".concat(Math.min(g,100),"%")}})}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,a.jsx)("span",{children:"0 ONION"}),(0,a.jsx)("span",{children:j(s?s.totalSupply:1e6)})]})]}),(null==s?void 0:s.auctionStatus)===f.NOT_STARTED&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.A,{className:"w-5 h-5 text-blue-600"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-blue-800",children:["Auction will start ",t,". Get ready to participate!"]})]})}),(null==s?void 0:s.auctionStatus)===f.PAUSED_BETWEEN_ROUNDS&&(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-yellow-600"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-yellow-800",children:["Calculating new round price. Next round starts ",t,"."]})]})}),(null==s?void 0:s.auctionStatus)===f.ENDED_SUCCESS&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800",children:"\uD83C\uDF89 Launch successful! Soft cap reached. Tokens will be distributed soon."})]})}),(null==s?void 0:s.auctionStatus)===f.ENDED_FAILED&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-red-800",children:"Launch failed. Soft cap not reached. Refunds will be processed."})]})}),(null==s?void 0:s.auctionStatus)===f.ACTIVE&&g>80&&(0,a.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 text-orange-600"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-orange-800",children:["Only ",j(s?s.totalSupply-s.totalTokensSold:327e3)," tokens remaining!"]})]})}),(null==s?void 0:s.auctionStatus)===f.ACTIVE&&s.remainingTokensInRound&&(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.A,{className:"w-5 h-5 text-blue-600"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-blue-800",children:[j(s.remainingTokensInRound)," tokens available in current round"]})]})})]})]})}var E=t(9474),T=t(8474),A=t(7072),D=t(3950),C=t(8453);function R(){let e=(0,E.c5)(),[s,t]=(0,m.useState)(""),[r,n]=(0,m.useState)("TON"),[l,c]=(0,m.useState)("signature"),{isCalculating:d,isProcessing:x,calculation:u,error:h,calculatePurchase:g,executePurchase:j,clearCalculation:f,clearError:b,getFormattedCalculation:y,isConnected:v}=function(){var e;let s=(0,E.c5)(),[t,a]=(0,m.useState)({isCalculating:!1,isProcessing:!1,calculation:null,error:null}),r=(0,m.useCallback)(async e=>{var t;if(!(null==s?void 0:null===(t=s.account)||void 0===t?void 0:t.address))return a(e=>({...e,error:"Wallet not connected"})),null;a(e=>({...e,isCalculating:!0,error:null,calculation:null}));try{let t=BigInt(Math.floor(1e9*parseFloat(e.amount))),r="TON"===e.currency?0:1,n=await N.calculatePurchase({user_address:s.account.address,amount:t.toString(),currency:r});if(!n.success)throw Error(n.error||"Calculation failed");return a(e=>({...e,isCalculating:!1,calculation:n})),n}catch(s){let e=p(s);return a(s=>({...s,isCalculating:!1,error:e})),null}},[s]),n=(0,m.useCallback)(async()=>{if(!s||!t.calculation)return a(e=>({...e,error:"No calculation available"})),!1;a(e=>({...e,isProcessing:!0,error:null}));try{return console.log("Executing purchase with signature:",{calculation:t.calculation.calculation,signature:t.calculation.signature}),await new Promise(e=>setTimeout(e,2e3)),a(e=>({...e,isProcessing:!1,calculation:null})),!0}catch(s){let e=p(s);return a(s=>({...s,isProcessing:!1,error:e})),!1}},[s,t.calculation]),l=(0,m.useCallback)(()=>{a(e=>({...e,calculation:null,error:null}))},[]),i=(0,m.useCallback)(()=>{a(e=>({...e,error:null}))},[]),c=(0,m.useCallback)(()=>{if(!t.calculation)return null;let e=t.calculation.calculation;return{amount:parseFloat(e.amount)/1e9,currency:0===e.currency?"TON":"USDT",tokensToReceive:parseFloat(e.tokens_to_receive)/1e9,currentPrice:parseFloat(e.current_price)/1e9,currentRound:e.current_round,timestamp:new Date(1e3*e.timestamp),nonce:e.nonce}},[t.calculation]);return{...t,calculatePurchase:r,executePurchase:n,clearCalculation:l,clearError:i,getFormattedCalculation:c,isConnected:!!(null==s?void 0:null===(e=s.account)||void 0===e?void 0:e.address)}}(),w=()=>{if(!s)return 0;let e=y();if(e)return e.tokensToReceive;let t=parseFloat(s);return("TON"===r?5.5*t:1*t)/.11},S=()=>{if(!s)return!1;let e=parseFloat(s);return("TON"===r?5.5*e:1*e)>=50},R=async()=>{S()&&(b(),await g({amount:s,currency:r}))},P=async()=>{if(e&&v){if("signature"===l){if(!u){await R();return}await j()&&(t(""),alert("Purchase successful!"))}else console.log("Processing direct purchase:",{amount:s,currency:r,tokens:w()}),await new Promise(e=>setTimeout(e,2e3)),t(""),alert("Purchase successful!")}},_=w(),F="TON"===r?"9.09":"50",k=y();return(0,a.jsxs)("div",{className:"bg-white rounded-2xl card-shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-onion-100 rounded-lg",children:(0,a.jsx)(T.A,{className:"w-5 h-5 text-onion-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Purchase ONION"}),"signature"===l&&(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-blue-600",children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Signature Verified"})]})]}),e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-onion-50 to-onion-100 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-onion-700",children:"Current Price"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-onion-800",children:["$",k?k.currentPrice.toFixed(3):"0.110"]})]}),(0,a.jsxs)("div",{className:"text-xs text-onion-600 mt-1",children:["per ONION token • Round ",k?k.currentRound:12]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Purchase Method"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("button",{onClick:()=>c("signature"),className:"p-3 rounded-lg border-2 transition-all ".concat("signature"===l?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 bg-white text-gray-700 hover:border-gray-300"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"font-medium",children:"Signature Verified"})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:"Off-chain calculation"})]}),(0,a.jsxs)("button",{onClick:()=>c("direct"),className:"p-3 rounded-lg border-2 transition-all ".concat("direct"===l?"border-green-500 bg-green-50 text-green-700":"border-gray-200 bg-white text-gray-700 hover:border-gray-300"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"font-medium",children:"Direct"})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:"On-chain calculation"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Currency"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("button",{onClick:()=>n("TON"),className:"p-3 rounded-lg border-2 transition-all ".concat("TON"===r?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 bg-white text-gray-700 hover:border-gray-300"),children:[(0,a.jsx)("div",{className:"font-medium",children:"TON"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["$","5.50"]})]}),(0,a.jsxs)("button",{onClick:()=>n("USDT"),className:"p-3 rounded-lg border-2 transition-all ".concat("USDT"===r?"border-green-500 bg-green-50 text-green-700":"border-gray-200 bg-white text-gray-700 hover:border-gray-300"),children:[(0,a.jsx)("div",{className:"font-medium",children:"USDT"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["$","1.00"]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Amount (",r,")"]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"number",value:s,onChange:e=>t(e.target.value),placeholder:"Min: ".concat(F," ").concat(r),className:"w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-onion-500 focus:border-transparent",step:"0.01"}),(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:r})})]}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:["Minimum: $",F," ",r," ($",50," USD equivalent)"]})]}),"signature"===l&&s&&S()&&!u&&(0,a.jsx)("button",{onClick:R,disabled:d,className:"w-full py-3 rounded-lg font-semibold transition-all bg-blue-500 text-white hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed",children:d?(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{children:"Calculating..."})]}):"Calculate Purchase"}),s&&("direct"===l||u)&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,a.jsx)(C.A,{className:"w-4 h-4 text-gray-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"signature"===l?"Verified Calculation":"Calculation"}),"signature"===l&&u&&(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-green-600",children:[(0,a.jsx)(o.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Signed"})]})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Amount in USD:"}),(0,a.jsxs)("span",{className:"font-medium",children:["$",k?(k.amount*("TON"===k.currency?5.5:1)).toFixed(2):((parseFloat(s)||0)*("TON"===r?5.5:1)).toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Price per ONION:"}),(0,a.jsxs)("span",{className:"font-medium",children:["$",k?k.currentPrice.toFixed(3):"0.110"]})]}),k&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Round:"}),(0,a.jsx)("span",{className:"font-medium",children:k.currentRound})]}),(0,a.jsxs)("div",{className:"border-t pt-2 flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:"You will receive:"}),(0,a.jsxs)("span",{className:"font-bold text-onion-600",children:[_.toLocaleString(void 0,{maximumFractionDigits:0})," ONION"]})]}),k&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-2",children:["Calculated at: ",k.timestamp.toLocaleTimeString(),"signature"===l&&(0,a.jsxs)("span",{className:"ml-2",children:["• Nonce: ",k.nonce]})]})]})]}),h&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg",children:[(0,a.jsx)(D.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:h}),(0,a.jsx)("button",{onClick:b,className:"ml-auto text-red-400 hover:text-red-600",children:"\xd7"})]}),s&&!S()&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-red-600 text-sm",children:[(0,a.jsx)(D.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Amount must be at least $",50," USD equivalent"]})]}),s&&S()&&!h&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-green-600 text-sm",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Valid purchase amount"})]}),u&&(0,a.jsx)("button",{onClick:f,className:"w-full py-2 rounded-lg font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 transition-all",children:"Clear Calculation"}),(0,a.jsx)("button",{onClick:P,disabled:!S()||x||"signature"===l&&!u&&!d,className:"w-full py-4 rounded-lg font-semibold transition-all ".concat(S()&&!x&&("signature"!==l||u||d)?"bg-gradient-to-r from-onion-500 to-onion-600 text-white hover:from-onion-600 hover:to-onion-700 transform hover:scale-[1.02]":"bg-gray-300 text-gray-500 cursor-not-allowed"),children:x?(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{children:"Processing..."})]}):"signature"!==l||u?"Purchase ONION Tokens":"Calculate First"}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(D.A,{className:"w-4 h-4 text-yellow-600"}),(0,a.jsx)("span",{className:"font-medium text-yellow-800",children:"Important Notice"})]}),(0,a.jsxs)("div",{className:"text-yellow-700 space-y-1",children:[(0,a.jsx)("p",{children:"• Refunds available during auction with 5% fee"}),(0,a.jsx)("p",{children:"• Tokens distributed after successful auction completion"}),(0,a.jsx)("p",{children:"• Price may increase in future rounds"}),"signature"===l&&(0,a.jsx)("p",{children:"• Signature verification ensures accurate pricing"})]})]})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4 mx-auto",children:(0,a.jsx)(D.A,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Connect Your Wallet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Connect your TON wallet to participate in the fair launch"})]})]})}var P=t(4947);function _(){let e=[{id:12,timeRange:"14:00 - 15:00 UTC",price:.11,volume:45230,endTime:"Active"},{id:11,timeRange:"13:00 - 14:00 UTC",price:.1,volume:52100,endTime:"14:00"},{id:10,timeRange:"12:00 - 13:00 UTC",price:.09,volume:38750,endTime:"13:00"},{id:9,timeRange:"11:00 - 12:00 UTC",price:.08,volume:41200,endTime:"12:00"},{id:8,timeRange:"10:00 - 11:00 UTC",price:.07,volume:35600,endTime:"11:00"},{id:7,timeRange:"09:00 - 10:00 UTC",price:.06,volume:28900,endTime:"10:00"},{id:6,timeRange:"08:00 - 09:00 UTC",price:.05,volume:32400,endTime:"09:00"}],s=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:3,maximumFractionDigits:3}).format(e),t=e=>new Intl.NumberFormat("en-US",{minimumFractionDigits:0,maximumFractionDigits:0}).format(e),r=(s,t)=>{if(t===e.length-1)return"text-gray-500";let a=e[t+1];return s.price>a.price?"text-green-600":s.price<a.price?"text-red-600":"text-gray-500"},n=(s,t)=>{if(t===e.length-1)return null;let a=e[t+1];return s.price>a.price?"↗":s.price<a.price?"↘":"→"};return(0,a.jsxs)("div",{className:"bg-white rounded-2xl card-shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-purple-100 rounded-lg",children:(0,a.jsx)(P.A,{className:"w-5 h-5 text-purple-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Auction History"})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,a.jsx)("th",{className:"text-left py-3 text-sm font-medium text-gray-600",children:"Round"}),(0,a.jsx)("th",{className:"text-left py-3 text-sm font-medium text-gray-600",children:"Time Range (UTC)"}),(0,a.jsx)("th",{className:"text-left py-3 text-sm font-medium text-gray-600",children:"Price"}),(0,a.jsx)("th",{className:"text-left py-3 text-sm font-medium text-gray-600",children:"Volume"}),(0,a.jsx)("th",{className:"text-left py-3 text-sm font-medium text-gray-600",children:"Status"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-100",children:e.map((e,l)=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors ".concat("Active"===e.endTime?"bg-green-50":""),children:[(0,a.jsx)("td",{className:"py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"font-semibold text-gray-900",children:["Round ",e.id]}),"Active"===e.endTime&&(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]})}),(0,a.jsx)("td",{className:"py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[(0,a.jsx)(i.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:e.timeRange})]})}),(0,a.jsx)("td",{className:"py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"font-semibold ".concat(r(e,l)),children:s(e.price)}),n(e,l)&&(0,a.jsx)("span",{className:"text-xs ".concat(r(e,l)),children:n(e,l)})]})}),(0,a.jsx)("td",{className:"py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:t(e.volume)}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"ONION"})]})}),(0,a.jsx)("td",{className:"py-4",children:"Active"===e.endTime?(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5 animate-pulse"}),"Active"]}):(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:["Ended ",e.endTime]})})]},e.id))})]})}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-3 gap-4 pt-6 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:"12"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Rounds"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"+83%"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Price Growth"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:t(e.reduce((e,s)=>e+s.volume,0))}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Volume"})]})]})]})}var F=t(1466),k=t(767),O=t(1773);function U(){let e=(0,E.c5)(),[s,t]=(0,m.useState)(null),[r,n]=(0,m.useState)([{id:1,amount:100,currency:"TON",tokens:5e3,price:.11,timestamp:"2025-01-12 14:30 UTC",status:"completed",canRefund:!0},{id:2,amount:200,currency:"USDT",tokens:1818,price:.11,timestamp:"2025-01-12 15:15 UTC",status:"pending",canRefund:!0}]),l=async s=>{if(e){t(s);try{console.log("Processing refund for purchase:",s),await new Promise(e=>setTimeout(e,2e3)),n(e=>e.map(e=>e.id===s?{...e,status:"refunded",canRefund:!1}:e)),alert("Refund processed successfully!")}catch(e){console.error("Refund failed:",e),alert("Refund failed. Please try again.")}finally{t(null)}}},i=(e,s)=>"".concat(e.toLocaleString()," ").concat(s),c=e=>"".concat(e.toLocaleString()," ONION");return e?(0,a.jsxs)("div",{className:"bg-white rounded-2xl card-shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg",children:(0,a.jsx)(F.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Your Purchases"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-700",children:c(r.filter(e=>"refunded"!==e.status).reduce((e,s)=>e+s.tokens,0))}),(0,a.jsx)("div",{className:"text-sm text-blue-600",children:"Total Purchased"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-700",children:["$",r.filter(e=>"refunded"!==e.status).reduce((e,s)=>e+("TON"===s.currency?5.5*s.amount:s.amount),0).toLocaleString()]}),(0,a.jsx)("div",{className:"text-sm text-green-600",children:"Total Invested"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Purchase History"}),0===r.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4 mx-auto",children:(0,a.jsx)(D.A,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("p",{className:"text-gray-600",children:"No purchases yet"})]}):(0,a.jsx)("div",{className:"space-y-3",children:r.map(e=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["Purchase #",e.id]}),"completed"===e.status&&(0,a.jsx)(o.A,{className:"w-4 h-4 text-green-500"}),"pending"===e.status&&(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"}),"refunded"===e.status&&(0,a.jsx)(k.A,{className:"w-4 h-4 text-red-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Amount:"}),(0,a.jsx)("span",{className:"ml-2 font-medium",children:i(e.amount,e.currency)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Tokens:"}),(0,a.jsx)("span",{className:"ml-2 font-medium",children:c(e.tokens)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Price:"}),(0,a.jsxs)("span",{className:"ml-2 font-medium",children:["$",e.price.toFixed(3)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Time:"}),(0,a.jsx)("span",{className:"ml-2 font-medium",children:e.timestamp})]})]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-2",children:["completed"===e.status&&(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Completed"}),"pending"===e.status&&(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"Pending"}),"refunded"===e.status&&(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"Refunded"})]})]}),e.canRefund&&"refunded"!==e.status&&(0,a.jsx)("button",{onClick:()=>l(e.id),disabled:s===e.id,className:"ml-4 px-3 py-1.5 text-sm font-medium text-red-600 border border-red-200 rounded-lg hover:bg-red-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:s===e.id?(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(O.A,{className:"w-3 h-3 animate-spin"}),(0,a.jsx)("span",{children:"Refunding..."})]}):"Refund"})]})},e.id))})]}),r.some(e=>e.canRefund&&"refunded"!==e.status)&&(0,a.jsxs)("div",{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(D.A,{className:"w-4 h-4 text-yellow-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-yellow-800",children:"Refund Policy"})]}),(0,a.jsxs)("div",{className:"text-sm text-yellow-700",children:[(0,a.jsx)("p",{children:"• Refunds available during auction period only"}),(0,a.jsx)("p",{children:"• 5% fee will be deducted from refund amount"}),(0,a.jsx)("p",{children:"• Refunds processed immediately upon confirmation"})]})]})]}):(0,a.jsxs)("div",{className:"bg-white rounded-2xl card-shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-gray-100 rounded-lg",children:(0,a.jsx)(F.A,{className:"w-5 h-5 text-gray-400"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Your Purchases"})]}),(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4 mx-auto",children:(0,a.jsx)(F.A,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("p",{className:"text-gray-600",children:"Connect wallet to view your purchases"})]})]})}function I(){let{auctionData:e,isLoading:s}=function(){let[e,s]=(0,m.useState)(null),[t,a]=(0,m.useState)(!0),[r,n]=(0,m.useState)(null),[l,i]=(0,m.useState)(!0);return(0,m.useEffect)(()=>{let e=async()=>{a(!0),n(null);try{let e=await N.getAuctionState(),t={totalRaised:parseFloat(j(e.total_raised)),totalTokensSold:parseFloat(j(e.total_tokens_sold)),auctionStatus:e.auction_status,currentRound:e.current_round,currentPrice:parseFloat(j(e.current_price)),endTime:Math.floor(Date.now()/1e3)+86400,softCap:5e5,hardCap:2e6,totalSupply:1e6,startTime:Math.floor(Date.now()/1e3)-43200,purchaseCount:e.purchase_count};s(t),i(!0)}catch(a){console.warn("API service unavailable, using mock data:",a),n(p(a)),i(!1);let e=Math.floor(Date.now()/1e3),t=f.ACTIVE;s({totalRaised:t===f.ENDED_FAILED?4e5:16e5,totalTokensSold:673e3,auctionStatus:t,currentRound:12,currentPrice:.11,lastRoundPrice:.1,endTime:e+86400,roundEndTime:t===f.ACTIVE?e+3600:void 0,nextRoundStartTime:t===f.PAUSED_BETWEEN_ROUNDS?e+300:void 0,softCap:5e5,hardCap:2e6,totalSupply:1e6,startTime:t===f.NOT_STARTED?e+7200:e-3600,purchaseCount:1250,remainingTokensInRound:t===f.ACTIVE?5e4:void 0})}finally{a(!1)}};e();let t=setInterval(e,3e4);return()=>clearInterval(t)},[]),{auctionData:e,isLoading:t,error:r,isOnline:l,clearError:()=>n(null)}}();return s?(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-onion-600"})})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-onion-500 to-onion-600 rounded-full mb-6",children:(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:"\uD83E\uDDC5"})}),(0,a.jsx)("h1",{className:"text-4xl lg:text-6xl font-bold bg-gradient-to-r from-onion-600 to-onion-700 bg-clip-text text-transparent mb-4",children:"ONION Token Fair Launch"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Experience the future of fair token distribution with our English auction mechanism. Equal opportunity for all participants with transparent pricing."})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,a.jsx)(S,{data:e||void 0}),(0,a.jsx)(b,{data:e||void 0}),(0,a.jsx)(_,{})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)(R,{}),(0,a.jsx)(U,{})]})]}),(0,a.jsxs)("footer",{className:"mt-16 pt-8 border-t border-gray-200 text-center text-gray-500",children:[(0,a.jsx)("p",{children:"\xa9 2025 ONION Token. All rights reserved."}),(0,a.jsx)("p",{className:"mt-2 text-sm",children:"Built on TON Blockchain with Tact smart contracts"})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[99,164,843,441,517,358],()=>s(9797)),_N_E=e.O()}]);