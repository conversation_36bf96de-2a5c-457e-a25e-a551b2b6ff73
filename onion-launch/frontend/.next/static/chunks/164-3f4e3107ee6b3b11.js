(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[164],{5020:e=>{"use strict";var t=function(e){var t;return!!e&&"object"==typeof e&&"[object RegExp]"!==(t=Object.prototype.toString.call(e))&&"[object Date]"!==t&&e.$$typeof!==n},n="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function i(e,t){return!1!==t.clone&&t.isMergeableObject(e)?a(Array.isArray(e)?[]:{},e,t):e}function r(e,t,n){return e.concat(t).map(function(e){return i(e,n)})}function o(e){return Object.keys(e).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[])}function s(e,t){try{return t in e}catch(e){return!1}}function a(e,n,l){(l=l||{}).arrayMerge=l.arrayMerge||r,l.isMergeableObject=l.isMergeableObject||t,l.cloneUnlessOtherwiseSpecified=i;var c,d,u=Array.isArray(n);return u!==Array.isArray(e)?i(n,l):u?l.arrayMerge(e,n,l):(d={},(c=l).isMergeableObject(e)&&o(e).forEach(function(t){d[t]=i(e[t],c)}),o(n).forEach(function(t){(!s(e,t)||Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))&&(s(e,t)&&c.isMergeableObject(n[t])?d[t]=(function(e,t){if(!t.customMerge)return a;var n=t.customMerge(e);return"function"==typeof n?n:a})(t,c)(e[t],n[t],c):d[t]=i(n[t],c))}),d)}a.all=function(e,t){if(!Array.isArray(e))throw Error("first argument should be an array");return e.reduce(function(e,n){return a(e,n,t)},{})},e.exports=a},7401:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var i=n(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,i.forwardRef)((e,t)=>{let{color:n="currentColor",size:r=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:c="",children:d,iconNode:u,...h}=e;return(0,i.createElement)("svg",{ref:t,...s,width:r,height:r,stroke:n,strokeWidth:l?24*Number(a)/Number(r):a,className:o("lucide",c),...h},[...u.map(e=>{let[t,n]=e;return(0,i.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),l=(e,t)=>{let n=(0,i.forwardRef)((n,s)=>{let{className:l,...c}=n;return(0,i.createElement)(a,{ref:s,iconNode:t,className:o("lucide-".concat(r(e)),l),...c})});return n.displayName="".concat(e),n}},6878:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(7401).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},8254:function(e,t,n){var i,r=n(6443).Buffer;i=function(){"use strict";var e={};function t(e){if(!/^(?:[A-Za-z0-9+\/]{2}[A-Za-z0-9+\/]{2})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=)?$/.test(e))throw TypeError("invalid encoding")}return e.decodeUTF8=function(e){if("string"!=typeof e)throw TypeError("expected string");var t,n=unescape(encodeURIComponent(e)),i=new Uint8Array(n.length);for(t=0;t<n.length;t++)i[t]=n.charCodeAt(t);return i},e.encodeUTF8=function(e){var t,n=[];for(t=0;t<e.length;t++)n.push(String.fromCharCode(e[t]));return decodeURIComponent(escape(n.join("")))},"undefined"==typeof atob?void 0!==r.from?(e.encodeBase64=function(e){return r.from(e).toString("base64")},e.decodeBase64=function(e){return t(e),new Uint8Array(Array.prototype.slice.call(r.from(e,"base64"),0))}):(e.encodeBase64=function(e){return new r(e).toString("base64")},e.decodeBase64=function(e){return t(e),new Uint8Array(Array.prototype.slice.call(new r(e,"base64"),0))}):(e.encodeBase64=function(e){var t,n=[],i=e.length;for(t=0;t<i;t++)n.push(String.fromCharCode(e[t]));return btoa(n.join(""))},e.decodeBase64=function(e){t(e);var n,i=atob(e),r=new Uint8Array(i.length);for(n=0;n<i.length;n++)r[n]=i.charCodeAt(n);return r}),e},e.exports?e.exports=i():(this.nacl||(this.nacl={}),this.nacl.util=i())},9089:(e,t,n)=>{!function(e){"use strict";var t,i=function(e){var t,n=new Float64Array(16);if(e)for(t=0;t<e.length;t++)n[t]=e[t];return n},r=function(){throw Error("no PRNG")},o=new Uint8Array(16),s=new Uint8Array(32);s[0]=9;var a=i(),l=i([1]),c=i([56129,1]),d=i([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),u=i([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),h=i([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),p=i([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),f=i([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function b(e,t,n,i){e[t]=n>>24&255,e[t+1]=n>>16&255,e[t+2]=n>>8&255,e[t+3]=255&n,e[t+4]=i>>24&255,e[t+5]=i>>16&255,e[t+6]=i>>8&255,e[t+7]=255&i}function g(e,t,n,i,r){var o,s=0;for(o=0;o<r;o++)s|=e[t+o]^n[i+o];return(1&s-1>>>8)-1}function w(e,t,n,i){return g(e,t,n,i,16)}function v(e,t,n,i){return g(e,t,n,i,32)}function y(e,t,n,i){!function(e,t,n,i){for(var r,o=255&i[0]|(255&i[1])<<8|(255&i[2])<<16|(255&i[3])<<24,s=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,l=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,c=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,d=255&i[4]|(255&i[5])<<8|(255&i[6])<<16|(255&i[7])<<24,u=255&t[0]|(255&t[1])<<8|(255&t[2])<<16|(255&t[3])<<24,h=255&t[4]|(255&t[5])<<8|(255&t[6])<<16|(255&t[7])<<24,p=255&t[8]|(255&t[9])<<8|(255&t[10])<<16|(255&t[11])<<24,f=255&t[12]|(255&t[13])<<8|(255&t[14])<<16|(255&t[15])<<24,b=255&i[8]|(255&i[9])<<8|(255&i[10])<<16|(255&i[11])<<24,g=255&n[16]|(255&n[17])<<8|(255&n[18])<<16|(255&n[19])<<24,w=255&n[20]|(255&n[21])<<8|(255&n[22])<<16|(255&n[23])<<24,v=255&n[24]|(255&n[25])<<8|(255&n[26])<<16|(255&n[27])<<24,y=255&n[28]|(255&n[29])<<8|(255&n[30])<<16|(255&n[31])<<24,m=255&i[12]|(255&i[13])<<8|(255&i[14])<<16|(255&i[15])<<24,x=o,_=s,S=a,E=l,R=c,k=d,O=u,C=h,T=p,A=f,U=b,N=g,P=w,j=v,L=y,M=m,W=0;W<20;W+=2)R^=(r=x+P|0)<<7|r>>>25,T^=(r=R+x|0)<<9|r>>>23,P^=(r=T+R|0)<<13|r>>>19,x^=(r=P+T|0)<<18|r>>>14,A^=(r=k+_|0)<<7|r>>>25,j^=(r=A+k|0)<<9|r>>>23,_^=(r=j+A|0)<<13|r>>>19,k^=(r=_+j|0)<<18|r>>>14,L^=(r=U+O|0)<<7|r>>>25,S^=(r=L+U|0)<<9|r>>>23,O^=(r=S+L|0)<<13|r>>>19,U^=(r=O+S|0)<<18|r>>>14,E^=(r=M+N|0)<<7|r>>>25,C^=(r=E+M|0)<<9|r>>>23,N^=(r=C+E|0)<<13|r>>>19,M^=(r=N+C|0)<<18|r>>>14,_^=(r=x+E|0)<<7|r>>>25,S^=(r=_+x|0)<<9|r>>>23,E^=(r=S+_|0)<<13|r>>>19,x^=(r=E+S|0)<<18|r>>>14,O^=(r=k+R|0)<<7|r>>>25,C^=(r=O+k|0)<<9|r>>>23,R^=(r=C+O|0)<<13|r>>>19,k^=(r=R+C|0)<<18|r>>>14,N^=(r=U+A|0)<<7|r>>>25,T^=(r=N+U|0)<<9|r>>>23,A^=(r=T+N|0)<<13|r>>>19,U^=(r=A+T|0)<<18|r>>>14,P^=(r=M+L|0)<<7|r>>>25,j^=(r=P+M|0)<<9|r>>>23,L^=(r=j+P|0)<<13|r>>>19,M^=(r=L+j|0)<<18|r>>>14;x=x+o|0,_=_+s|0,S=S+a|0,E=E+l|0,R=R+c|0,k=k+d|0,O=O+u|0,C=C+h|0,T=T+p|0,A=A+f|0,U=U+b|0,N=N+g|0,P=P+w|0,j=j+v|0,L=L+y|0,M=M+m|0,e[0]=x>>>0&255,e[1]=x>>>8&255,e[2]=x>>>16&255,e[3]=x>>>24&255,e[4]=_>>>0&255,e[5]=_>>>8&255,e[6]=_>>>16&255,e[7]=_>>>24&255,e[8]=S>>>0&255,e[9]=S>>>8&255,e[10]=S>>>16&255,e[11]=S>>>24&255,e[12]=E>>>0&255,e[13]=E>>>8&255,e[14]=E>>>16&255,e[15]=E>>>24&255,e[16]=R>>>0&255,e[17]=R>>>8&255,e[18]=R>>>16&255,e[19]=R>>>24&255,e[20]=k>>>0&255,e[21]=k>>>8&255,e[22]=k>>>16&255,e[23]=k>>>24&255,e[24]=O>>>0&255,e[25]=O>>>8&255,e[26]=O>>>16&255,e[27]=O>>>24&255,e[28]=C>>>0&255,e[29]=C>>>8&255,e[30]=C>>>16&255,e[31]=C>>>24&255,e[32]=T>>>0&255,e[33]=T>>>8&255,e[34]=T>>>16&255,e[35]=T>>>24&255,e[36]=A>>>0&255,e[37]=A>>>8&255,e[38]=A>>>16&255,e[39]=A>>>24&255,e[40]=U>>>0&255,e[41]=U>>>8&255,e[42]=U>>>16&255,e[43]=U>>>24&255,e[44]=N>>>0&255,e[45]=N>>>8&255,e[46]=N>>>16&255,e[47]=N>>>24&255,e[48]=P>>>0&255,e[49]=P>>>8&255,e[50]=P>>>16&255,e[51]=P>>>24&255,e[52]=j>>>0&255,e[53]=j>>>8&255,e[54]=j>>>16&255,e[55]=j>>>24&255,e[56]=L>>>0&255,e[57]=L>>>8&255,e[58]=L>>>16&255,e[59]=L>>>24&255,e[60]=M>>>0&255,e[61]=M>>>8&255,e[62]=M>>>16&255,e[63]=M>>>24&255}(e,t,n,i)}function m(e,t,n,i){!function(e,t,n,i){for(var r,o=255&i[0]|(255&i[1])<<8|(255&i[2])<<16|(255&i[3])<<24,s=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,l=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,c=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,d=255&i[4]|(255&i[5])<<8|(255&i[6])<<16|(255&i[7])<<24,u=255&t[0]|(255&t[1])<<8|(255&t[2])<<16|(255&t[3])<<24,h=255&t[4]|(255&t[5])<<8|(255&t[6])<<16|(255&t[7])<<24,p=255&t[8]|(255&t[9])<<8|(255&t[10])<<16|(255&t[11])<<24,f=255&t[12]|(255&t[13])<<8|(255&t[14])<<16|(255&t[15])<<24,b=255&i[8]|(255&i[9])<<8|(255&i[10])<<16|(255&i[11])<<24,g=255&n[16]|(255&n[17])<<8|(255&n[18])<<16|(255&n[19])<<24,w=255&n[20]|(255&n[21])<<8|(255&n[22])<<16|(255&n[23])<<24,v=255&n[24]|(255&n[25])<<8|(255&n[26])<<16|(255&n[27])<<24,y=255&n[28]|(255&n[29])<<8|(255&n[30])<<16|(255&n[31])<<24,m=255&i[12]|(255&i[13])<<8|(255&i[14])<<16|(255&i[15])<<24,x=o,_=s,S=a,E=l,R=c,k=d,O=u,C=h,T=p,A=f,U=b,N=g,P=w,j=v,L=y,M=m,W=0;W<20;W+=2)R^=(r=x+P|0)<<7|r>>>25,T^=(r=R+x|0)<<9|r>>>23,P^=(r=T+R|0)<<13|r>>>19,x^=(r=P+T|0)<<18|r>>>14,A^=(r=k+_|0)<<7|r>>>25,j^=(r=A+k|0)<<9|r>>>23,_^=(r=j+A|0)<<13|r>>>19,k^=(r=_+j|0)<<18|r>>>14,L^=(r=U+O|0)<<7|r>>>25,S^=(r=L+U|0)<<9|r>>>23,O^=(r=S+L|0)<<13|r>>>19,U^=(r=O+S|0)<<18|r>>>14,E^=(r=M+N|0)<<7|r>>>25,C^=(r=E+M|0)<<9|r>>>23,N^=(r=C+E|0)<<13|r>>>19,M^=(r=N+C|0)<<18|r>>>14,_^=(r=x+E|0)<<7|r>>>25,S^=(r=_+x|0)<<9|r>>>23,E^=(r=S+_|0)<<13|r>>>19,x^=(r=E+S|0)<<18|r>>>14,O^=(r=k+R|0)<<7|r>>>25,C^=(r=O+k|0)<<9|r>>>23,R^=(r=C+O|0)<<13|r>>>19,k^=(r=R+C|0)<<18|r>>>14,N^=(r=U+A|0)<<7|r>>>25,T^=(r=N+U|0)<<9|r>>>23,A^=(r=T+N|0)<<13|r>>>19,U^=(r=A+T|0)<<18|r>>>14,P^=(r=M+L|0)<<7|r>>>25,j^=(r=P+M|0)<<9|r>>>23,L^=(r=j+P|0)<<13|r>>>19,M^=(r=L+j|0)<<18|r>>>14;e[0]=x>>>0&255,e[1]=x>>>8&255,e[2]=x>>>16&255,e[3]=x>>>24&255,e[4]=k>>>0&255,e[5]=k>>>8&255,e[6]=k>>>16&255,e[7]=k>>>24&255,e[8]=U>>>0&255,e[9]=U>>>8&255,e[10]=U>>>16&255,e[11]=U>>>24&255,e[12]=M>>>0&255,e[13]=M>>>8&255,e[14]=M>>>16&255,e[15]=M>>>24&255,e[16]=O>>>0&255,e[17]=O>>>8&255,e[18]=O>>>16&255,e[19]=O>>>24&255,e[20]=C>>>0&255,e[21]=C>>>8&255,e[22]=C>>>16&255,e[23]=C>>>24&255,e[24]=T>>>0&255,e[25]=T>>>8&255,e[26]=T>>>16&255,e[27]=T>>>24&255,e[28]=A>>>0&255,e[29]=A>>>8&255,e[30]=A>>>16&255,e[31]=A>>>24&255}(e,t,n,i)}var x=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]);function _(e,t,n,i,r,o,s){var a,l,c=new Uint8Array(16),d=new Uint8Array(64);for(l=0;l<16;l++)c[l]=0;for(l=0;l<8;l++)c[l]=o[l];for(;r>=64;){for(y(d,c,s,x),l=0;l<64;l++)e[t+l]=n[i+l]^d[l];for(l=8,a=1;l<16;l++)a=a+(255&c[l])|0,c[l]=255&a,a>>>=8;r-=64,t+=64,i+=64}if(r>0)for(y(d,c,s,x),l=0;l<r;l++)e[t+l]=n[i+l]^d[l];return 0}function S(e,t,n,i,r){var o,s,a=new Uint8Array(16),l=new Uint8Array(64);for(s=0;s<16;s++)a[s]=0;for(s=0;s<8;s++)a[s]=i[s];for(;n>=64;){for(y(l,a,r,x),s=0;s<64;s++)e[t+s]=l[s];for(s=8,o=1;s<16;s++)o=o+(255&a[s])|0,a[s]=255&o,o>>>=8;n-=64,t+=64}if(n>0)for(y(l,a,r,x),s=0;s<n;s++)e[t+s]=l[s];return 0}function E(e,t,n,i,r){var o=new Uint8Array(32);m(o,i,r,x);for(var s=new Uint8Array(8),a=0;a<8;a++)s[a]=i[a+16];return S(e,t,n,s,o)}function R(e,t,n,i,r,o,s){var a=new Uint8Array(32);m(a,o,s,x);for(var l=new Uint8Array(8),c=0;c<8;c++)l[c]=o[c+16];return _(e,t,n,i,r,l,a)}var k=function(e){var t,n,i,r,o,s,a,l;this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.leftover=0,this.fin=0,t=255&e[0]|(255&e[1])<<8,this.r[0]=8191&t,n=255&e[2]|(255&e[3])<<8,this.r[1]=(t>>>13|n<<3)&8191,i=255&e[4]|(255&e[5])<<8,this.r[2]=(n>>>10|i<<6)&7939,r=255&e[6]|(255&e[7])<<8,this.r[3]=(i>>>7|r<<9)&8191,o=255&e[8]|(255&e[9])<<8,this.r[4]=(r>>>4|o<<12)&255,this.r[5]=o>>>1&8190,s=255&e[10]|(255&e[11])<<8,this.r[6]=(o>>>14|s<<2)&8191,a=255&e[12]|(255&e[13])<<8,this.r[7]=(s>>>11|a<<5)&8065,l=255&e[14]|(255&e[15])<<8,this.r[8]=(a>>>8|l<<8)&8191,this.r[9]=l>>>5&127,this.pad[0]=255&e[16]|(255&e[17])<<8,this.pad[1]=255&e[18]|(255&e[19])<<8,this.pad[2]=255&e[20]|(255&e[21])<<8,this.pad[3]=255&e[22]|(255&e[23])<<8,this.pad[4]=255&e[24]|(255&e[25])<<8,this.pad[5]=255&e[26]|(255&e[27])<<8,this.pad[6]=255&e[28]|(255&e[29])<<8,this.pad[7]=255&e[30]|(255&e[31])<<8};function O(e,t,n,i,r,o){var s=new k(o);return s.update(n,i,r),s.finish(e,t),0}function C(e,t,n,i,r,o){var s=new Uint8Array(16);return O(s,0,n,i,r,o),w(e,t,s,0)}function T(e,t,n,i,r){var o;if(n<32)return -1;for(R(e,0,t,0,n,i,r),O(e,16,e,32,n-32,e),o=0;o<16;o++)e[o]=0;return 0}function A(e,t,n,i,r){var o,s=new Uint8Array(32);if(n<32||(E(s,0,32,i,r),0!==C(t,16,t,32,n-32,s)))return -1;for(R(e,0,t,0,n,i,r),o=0;o<32;o++)e[o]=0;return 0}function U(e,t){var n;for(n=0;n<16;n++)e[n]=0|t[n]}function N(e){var t,n,i=1;for(t=0;t<16;t++)i=Math.floor((n=e[t]+i+65535)/65536),e[t]=n-65536*i;e[0]+=i-1+37*(i-1)}function P(e,t,n){for(var i,r=~(n-1),o=0;o<16;o++)i=r&(e[o]^t[o]),e[o]^=i,t[o]^=i}function j(e,t){var n,r,o,s=i(),a=i();for(n=0;n<16;n++)a[n]=t[n];for(N(a),N(a),N(a),r=0;r<2;r++){for(n=1,s[0]=a[0]-65517;n<15;n++)s[n]=a[n]-65535-(s[n-1]>>16&1),s[n-1]&=65535;s[15]=a[15]-32767-(s[14]>>16&1),o=s[15]>>16&1,s[14]&=65535,P(a,s,1-o)}for(n=0;n<16;n++)e[2*n]=255&a[n],e[2*n+1]=a[n]>>8}function L(e,t){var n=new Uint8Array(32),i=new Uint8Array(32);return j(n,e),j(i,t),v(n,0,i,0)}function M(e){var t=new Uint8Array(32);return j(t,e),1&t[0]}function W(e,t){var n;for(n=0;n<16;n++)e[n]=t[2*n]+(t[2*n+1]<<8);e[15]&=32767}function I(e,t,n){for(var i=0;i<16;i++)e[i]=t[i]+n[i]}function q(e,t,n){for(var i=0;i<16;i++)e[i]=t[i]-n[i]}function D(e,t,n){var i,r,o=0,s=0,a=0,l=0,c=0,d=0,u=0,h=0,p=0,f=0,b=0,g=0,w=0,v=0,y=0,m=0,x=0,_=0,S=0,E=0,R=0,k=0,O=0,C=0,T=0,A=0,U=0,N=0,P=0,j=0,L=0,M=n[0],W=n[1],I=n[2],q=n[3],D=n[4],B=n[5],K=n[6],F=n[7],G=n[8],$=n[9],z=n[10],H=n[11],V=n[12],Y=n[13],J=n[14],Q=n[15];o+=(i=t[0])*M,s+=i*W,a+=i*I,l+=i*q,c+=i*D,d+=i*B,u+=i*K,h+=i*F,p+=i*G,f+=i*$,b+=i*z,g+=i*H,w+=i*V,v+=i*Y,y+=i*J,m+=i*Q,s+=(i=t[1])*M,a+=i*W,l+=i*I,c+=i*q,d+=i*D,u+=i*B,h+=i*K,p+=i*F,f+=i*G,b+=i*$,g+=i*z,w+=i*H,v+=i*V,y+=i*Y,m+=i*J,x+=i*Q,a+=(i=t[2])*M,l+=i*W,c+=i*I,d+=i*q,u+=i*D,h+=i*B,p+=i*K,f+=i*F,b+=i*G,g+=i*$,w+=i*z,v+=i*H,y+=i*V,m+=i*Y,x+=i*J,_+=i*Q,l+=(i=t[3])*M,c+=i*W,d+=i*I,u+=i*q,h+=i*D,p+=i*B,f+=i*K,b+=i*F,g+=i*G,w+=i*$,v+=i*z,y+=i*H,m+=i*V,x+=i*Y,_+=i*J,S+=i*Q,c+=(i=t[4])*M,d+=i*W,u+=i*I,h+=i*q,p+=i*D,f+=i*B,b+=i*K,g+=i*F,w+=i*G,v+=i*$,y+=i*z,m+=i*H,x+=i*V,_+=i*Y,S+=i*J,E+=i*Q,d+=(i=t[5])*M,u+=i*W,h+=i*I,p+=i*q,f+=i*D,b+=i*B,g+=i*K,w+=i*F,v+=i*G,y+=i*$,m+=i*z,x+=i*H,_+=i*V,S+=i*Y,E+=i*J,R+=i*Q,u+=(i=t[6])*M,h+=i*W,p+=i*I,f+=i*q,b+=i*D,g+=i*B,w+=i*K,v+=i*F,y+=i*G,m+=i*$,x+=i*z,_+=i*H,S+=i*V,E+=i*Y,R+=i*J,k+=i*Q,h+=(i=t[7])*M,p+=i*W,f+=i*I,b+=i*q,g+=i*D,w+=i*B,v+=i*K,y+=i*F,m+=i*G,x+=i*$,_+=i*z,S+=i*H,E+=i*V,R+=i*Y,k+=i*J,O+=i*Q,p+=(i=t[8])*M,f+=i*W,b+=i*I,g+=i*q,w+=i*D,v+=i*B,y+=i*K,m+=i*F,x+=i*G,_+=i*$,S+=i*z,E+=i*H,R+=i*V,k+=i*Y,O+=i*J,C+=i*Q,f+=(i=t[9])*M,b+=i*W,g+=i*I,w+=i*q,v+=i*D,y+=i*B,m+=i*K,x+=i*F,_+=i*G,S+=i*$,E+=i*z,R+=i*H,k+=i*V,O+=i*Y,C+=i*J,T+=i*Q,b+=(i=t[10])*M,g+=i*W,w+=i*I,v+=i*q,y+=i*D,m+=i*B,x+=i*K,_+=i*F,S+=i*G,E+=i*$,R+=i*z,k+=i*H,O+=i*V,C+=i*Y,T+=i*J,A+=i*Q,g+=(i=t[11])*M,w+=i*W,v+=i*I,y+=i*q,m+=i*D,x+=i*B,_+=i*K,S+=i*F,E+=i*G,R+=i*$,k+=i*z,O+=i*H,C+=i*V,T+=i*Y,A+=i*J,U+=i*Q,w+=(i=t[12])*M,v+=i*W,y+=i*I,m+=i*q,x+=i*D,_+=i*B,S+=i*K,E+=i*F,R+=i*G,k+=i*$,O+=i*z,C+=i*H,T+=i*V,A+=i*Y,U+=i*J,N+=i*Q,v+=(i=t[13])*M,y+=i*W,m+=i*I,x+=i*q,_+=i*D,S+=i*B,E+=i*K,R+=i*F,k+=i*G,O+=i*$,C+=i*z,T+=i*H,A+=i*V,U+=i*Y,N+=i*J,P+=i*Q,y+=(i=t[14])*M,m+=i*W,x+=i*I,_+=i*q,S+=i*D,E+=i*B,R+=i*K,k+=i*F,O+=i*G,C+=i*$,T+=i*z,A+=i*H,U+=i*V,N+=i*Y,P+=i*J,j+=i*Q,m+=(i=t[15])*M,x+=i*W,_+=i*I,S+=i*q,E+=i*D,R+=i*B,k+=i*K,O+=i*F,C+=i*G,T+=i*$,A+=i*z,U+=i*H,N+=i*V,P+=i*Y,j+=i*J,L+=i*Q,o+=38*x,s+=38*_,a+=38*S,l+=38*E,c+=38*R,d+=38*k,u+=38*O,h+=38*C,p+=38*T,f+=38*A,b+=38*U,g+=38*N,w+=38*P,v+=38*j,y+=38*L,r=Math.floor((i=o+(r=1)+65535)/65536),o=i-65536*r,r=Math.floor((i=s+r+65535)/65536),s=i-65536*r,r=Math.floor((i=a+r+65535)/65536),a=i-65536*r,r=Math.floor((i=l+r+65535)/65536),l=i-65536*r,r=Math.floor((i=c+r+65535)/65536),c=i-65536*r,r=Math.floor((i=d+r+65535)/65536),d=i-65536*r,r=Math.floor((i=u+r+65535)/65536),u=i-65536*r,r=Math.floor((i=h+r+65535)/65536),h=i-65536*r,r=Math.floor((i=p+r+65535)/65536),p=i-65536*r,r=Math.floor((i=f+r+65535)/65536),f=i-65536*r,r=Math.floor((i=b+r+65535)/65536),b=i-65536*r,r=Math.floor((i=g+r+65535)/65536),g=i-65536*r,r=Math.floor((i=w+r+65535)/65536),w=i-65536*r,r=Math.floor((i=v+r+65535)/65536),v=i-65536*r,r=Math.floor((i=y+r+65535)/65536),y=i-65536*r,r=Math.floor((i=m+r+65535)/65536),m=i-65536*r,o+=r-1+37*(r-1),r=Math.floor((i=o+(r=1)+65535)/65536),o=i-65536*r,r=Math.floor((i=s+r+65535)/65536),s=i-65536*r,r=Math.floor((i=a+r+65535)/65536),a=i-65536*r,r=Math.floor((i=l+r+65535)/65536),l=i-65536*r,r=Math.floor((i=c+r+65535)/65536),c=i-65536*r,r=Math.floor((i=d+r+65535)/65536),d=i-65536*r,r=Math.floor((i=u+r+65535)/65536),u=i-65536*r,r=Math.floor((i=h+r+65535)/65536),h=i-65536*r,r=Math.floor((i=p+r+65535)/65536),p=i-65536*r,r=Math.floor((i=f+r+65535)/65536),f=i-65536*r,r=Math.floor((i=b+r+65535)/65536),b=i-65536*r,r=Math.floor((i=g+r+65535)/65536),g=i-65536*r,r=Math.floor((i=w+r+65535)/65536),w=i-65536*r,r=Math.floor((i=v+r+65535)/65536),v=i-65536*r,r=Math.floor((i=y+r+65535)/65536),y=i-65536*r,r=Math.floor((i=m+r+65535)/65536),m=i-65536*r,o+=r-1+37*(r-1),e[0]=o,e[1]=s,e[2]=a,e[3]=l,e[4]=c,e[5]=d,e[6]=u,e[7]=h,e[8]=p,e[9]=f,e[10]=b,e[11]=g,e[12]=w,e[13]=v,e[14]=y,e[15]=m}function B(e,t){D(e,t,t)}function K(e,t){var n,r=i();for(n=0;n<16;n++)r[n]=t[n];for(n=253;n>=0;n--)B(r,r),2!==n&&4!==n&&D(r,r,t);for(n=0;n<16;n++)e[n]=r[n]}function F(e,t){var n,r=i();for(n=0;n<16;n++)r[n]=t[n];for(n=250;n>=0;n--)B(r,r),1!==n&&D(r,r,t);for(n=0;n<16;n++)e[n]=r[n]}function G(e,t,n){var r,o,s=new Uint8Array(32),a=new Float64Array(80),l=i(),d=i(),u=i(),h=i(),p=i(),f=i();for(o=0;o<31;o++)s[o]=t[o];for(s[31]=127&t[31]|64,s[0]&=248,W(a,n),o=0;o<16;o++)d[o]=a[o],h[o]=l[o]=u[o]=0;for(o=254,l[0]=h[0]=1;o>=0;--o)P(l,d,r=s[o>>>3]>>>(7&o)&1),P(u,h,r),I(p,l,u),q(l,l,u),I(u,d,h),q(d,d,h),B(h,p),B(f,l),D(l,u,l),D(u,d,p),I(p,l,u),q(l,l,u),B(d,l),q(u,h,f),D(l,u,c),I(l,l,h),D(u,u,l),D(l,h,f),D(h,d,a),B(d,p),P(l,d,r),P(u,h,r);for(o=0;o<16;o++)a[o+16]=l[o],a[o+32]=u[o],a[o+48]=d[o],a[o+64]=h[o];var b=a.subarray(32),g=a.subarray(16);return K(b,b),D(g,g,b),j(e,g),0}function $(e,t){return G(e,t,s)}function z(e,t){return r(t,32),$(e,t)}function H(e,t,n){var i=new Uint8Array(32);return G(i,n,t),m(e,o,i,x)}k.prototype.blocks=function(e,t,n){for(var i,r,o,s,a,l,c,d,u,h,p,f,b,g,w,v,y,m,x,_=this.fin?0:2048,S=this.h[0],E=this.h[1],R=this.h[2],k=this.h[3],O=this.h[4],C=this.h[5],T=this.h[6],A=this.h[7],U=this.h[8],N=this.h[9],P=this.r[0],j=this.r[1],L=this.r[2],M=this.r[3],W=this.r[4],I=this.r[5],q=this.r[6],D=this.r[7],B=this.r[8],K=this.r[9];n>=16;)S+=8191&(i=255&e[t+0]|(255&e[t+1])<<8),E+=(i>>>13|(r=255&e[t+2]|(255&e[t+3])<<8)<<3)&8191,R+=(r>>>10|(o=255&e[t+4]|(255&e[t+5])<<8)<<6)&8191,k+=(o>>>7|(s=255&e[t+6]|(255&e[t+7])<<8)<<9)&8191,O+=(s>>>4|(a=255&e[t+8]|(255&e[t+9])<<8)<<12)&8191,C+=a>>>1&8191,T+=(a>>>14|(l=255&e[t+10]|(255&e[t+11])<<8)<<2)&8191,A+=(l>>>11|(c=255&e[t+12]|(255&e[t+13])<<8)<<5)&8191,U+=(c>>>8|(d=255&e[t+14]|(255&e[t+15])<<8)<<8)&8191,N+=d>>>5|_,u=(h=(u=0)+S*P+5*K*E+5*B*R+5*D*k+5*q*O)>>>13,h&=8191,h+=5*I*C+5*W*T+5*M*A+5*L*U+5*j*N,u+=h>>>13,h&=8191,u=(p=u+S*j+E*P+5*K*R+5*B*k+5*D*O)>>>13,p&=8191,p+=5*q*C,p+=5*I*T,p+=5*W*A,p+=5*M*U,p+=5*L*N,u+=p>>>13,p&=8191,u=(f=u+S*L+E*j+R*P+5*K*k+5*B*O)>>>13,f&=8191,f+=5*D*C,f+=5*q*T,f+=5*I*A,f+=5*W*U,f+=5*M*N,u+=f>>>13,f&=8191,u=(b=u+S*M+E*L+R*j+k*P+5*K*O)>>>13,b&=8191,b+=5*B*C,b+=5*D*T,b+=5*q*A,b+=5*I*U,b+=5*W*N,u+=b>>>13,b&=8191,u=(g=u+S*W+E*M+R*L+k*j+O*P)>>>13,g&=8191,g+=5*K*C,g+=5*B*T,g+=5*D*A,g+=5*q*U,g+=5*I*N,u+=g>>>13,g&=8191,u=(w=u+S*I+E*W+R*M+k*L+O*j)>>>13,w&=8191,w+=C*P,w+=5*K*T,w+=5*B*A,w+=5*D*U,w+=5*q*N,u+=w>>>13,w&=8191,u=(v=u+S*q+E*I+R*W+k*M+O*L)>>>13,v&=8191,v+=C*j,v+=T*P,v+=5*K*A,v+=5*B*U,v+=5*D*N,u+=v>>>13,v&=8191,u=(y=u+S*D+E*q+R*I+k*W+O*M)>>>13,y&=8191,y+=C*L,y+=T*j,y+=A*P,y+=5*K*U,y+=5*B*N,u+=y>>>13,y&=8191,u=(m=u+S*B+E*D+R*q+k*I+O*W)>>>13,m&=8191,m+=C*M,m+=T*L,m+=A*j,m+=U*P,m+=5*K*N,u+=m>>>13,m&=8191,u=(x=u+S*K+E*B+R*D+k*q+O*I)>>>13,x&=8191,x+=C*W,x+=T*M,x+=A*L,x+=U*j,x+=N*P,u+=x>>>13,x&=8191,h=8191&(u=(u=(u<<2)+u|0)+h|0),u>>>=13,p+=u,S=h,E=p,R=f,k=b,O=g,C=w,T=v,A=y,U=m,N=x,t+=16,n-=16;this.h[0]=S,this.h[1]=E,this.h[2]=R,this.h[3]=k,this.h[4]=O,this.h[5]=C,this.h[6]=T,this.h[7]=A,this.h[8]=U,this.h[9]=N},k.prototype.finish=function(e,t){var n,i,r,o,s=new Uint16Array(10);if(this.leftover){for(o=this.leftover,this.buffer[o++]=1;o<16;o++)this.buffer[o]=0;this.fin=1,this.blocks(this.buffer,0,16)}for(n=this.h[1]>>>13,this.h[1]&=8191,o=2;o<10;o++)this.h[o]+=n,n=this.h[o]>>>13,this.h[o]&=8191;for(this.h[0]+=5*n,n=this.h[0]>>>13,this.h[0]&=8191,this.h[1]+=n,n=this.h[1]>>>13,this.h[1]&=8191,this.h[2]+=n,s[0]=this.h[0]+5,n=s[0]>>>13,s[0]&=8191,o=1;o<10;o++)s[o]=this.h[o]+n,n=s[o]>>>13,s[o]&=8191;for(s[9]-=8192,i=(1^n)-1,o=0;o<10;o++)s[o]&=i;for(o=0,i=~i;o<10;o++)this.h[o]=this.h[o]&i|s[o];for(o=1,this.h[0]=(this.h[0]|this.h[1]<<13)&65535,this.h[1]=(this.h[1]>>>3|this.h[2]<<10)&65535,this.h[2]=(this.h[2]>>>6|this.h[3]<<7)&65535,this.h[3]=(this.h[3]>>>9|this.h[4]<<4)&65535,this.h[4]=(this.h[4]>>>12|this.h[5]<<1|this.h[6]<<14)&65535,this.h[5]=(this.h[6]>>>2|this.h[7]<<11)&65535,this.h[6]=(this.h[7]>>>5|this.h[8]<<8)&65535,this.h[7]=(this.h[8]>>>8|this.h[9]<<5)&65535,r=this.h[0]+this.pad[0],this.h[0]=65535&r;o<8;o++)r=(this.h[o]+this.pad[o]|0)+(r>>>16)|0,this.h[o]=65535&r;e[t+0]=this.h[0]>>>0&255,e[t+1]=this.h[0]>>>8&255,e[t+2]=this.h[1]>>>0&255,e[t+3]=this.h[1]>>>8&255,e[t+4]=this.h[2]>>>0&255,e[t+5]=this.h[2]>>>8&255,e[t+6]=this.h[3]>>>0&255,e[t+7]=this.h[3]>>>8&255,e[t+8]=this.h[4]>>>0&255,e[t+9]=this.h[4]>>>8&255,e[t+10]=this.h[5]>>>0&255,e[t+11]=this.h[5]>>>8&255,e[t+12]=this.h[6]>>>0&255,e[t+13]=this.h[6]>>>8&255,e[t+14]=this.h[7]>>>0&255,e[t+15]=this.h[7]>>>8&255},k.prototype.update=function(e,t,n){var i,r;if(this.leftover){for((r=16-this.leftover)>n&&(r=n),i=0;i<r;i++)this.buffer[this.leftover+i]=e[t+i];if(n-=r,t+=r,this.leftover+=r,this.leftover<16)return;this.blocks(this.buffer,0,16),this.leftover=0}if(n>=16&&(r=n-n%16,this.blocks(e,t,r),t+=r,n-=r),n){for(i=0;i<n;i++)this.buffer[this.leftover+i]=e[t+i];this.leftover+=n}};var V=[0x428a2f98,0xd728ae22,0x71374491,0x23ef65cd,0xb5c0fbcf,0xec4d3b2f,0xe9b5dba5,0x8189dbbc,0x3956c25b,0xf348b538,0x59f111f1,0xb605d019,0x923f82a4,0xaf194f9b,0xab1c5ed5,0xda6d8118,0xd807aa98,0xa3030242,0x12835b01,0x45706fbe,0x243185be,0x4ee4b28c,0x550c7dc3,0xd5ffb4e2,0x72be5d74,0xf27b896f,0x80deb1fe,0x3b1696b1,0x9bdc06a7,0x25c71235,0xc19bf174,0xcf692694,0xe49b69c1,0x9ef14ad2,0xefbe4786,0x384f25e3,0xfc19dc6,0x8b8cd5b5,0x240ca1cc,0x77ac9c65,0x2de92c6f,0x592b0275,0x4a7484aa,0x6ea6e483,0x5cb0a9dc,0xbd41fbd4,0x76f988da,0x831153b5,0x983e5152,0xee66dfab,0xa831c66d,0x2db43210,0xb00327c8,0x98fb213f,0xbf597fc7,0xbeef0ee4,0xc6e00bf3,0x3da88fc2,0xd5a79147,0x930aa725,0x6ca6351,0xe003826f,0x14292967,0xa0e6e70,0x27b70a85,0x46d22ffc,0x2e1b2138,0x5c26c926,0x4d2c6dfc,0x5ac42aed,0x53380d13,0x9d95b3df,0x650a7354,0x8baf63de,0x766a0abb,0x3c77b2a8,0x81c2c92e,0x47edaee6,0x92722c85,0x1482353b,0xa2bfe8a1,0x4cf10364,0xa81a664b,0xbc423001,0xc24b8b70,0xd0f89791,0xc76c51a3,0x654be30,0xd192e819,0xd6ef5218,0xd6990624,0x5565a910,0xf40e3585,0x5771202a,0x106aa070,0x32bbd1b8,0x19a4c116,0xb8d2d0c8,0x1e376c08,0x5141ab53,0x2748774c,0xdf8eeb99,0x34b0bcb5,0xe19b48a8,0x391c0cb3,0xc5c95a63,0x4ed8aa4a,0xe3418acb,0x5b9cca4f,0x7763e373,0x682e6ff3,0xd6b2b8a3,0x748f82ee,0x5defb2fc,0x78a5636f,0x43172f60,0x84c87814,0xa1f0ab72,0x8cc70208,0x1a6439ec,0x90befffa,0x23631e28,0xa4506ceb,0xde82bde9,0xbef9a3f7,0xb2c67915,0xc67178f2,0xe372532b,0xca273ece,0xea26619c,0xd186b8c7,0x21c0c207,0xeada7dd6,0xcde0eb1e,0xf57d4f7f,0xee6ed178,0x6f067aa,0x72176fba,0xa637dc5,0xa2c898a6,0x113f9804,0xbef90dae,0x1b710b35,0x131c471b,0x28db77f5,0x23047d84,0x32caab7b,0x40c72493,0x3c9ebe0a,0x15c9bebc,0x431d67c4,0x9c100d4c,0x4cc5d4be,0xcb3e42b6,0x597f299c,0xfc657e2a,0x5fcb6fab,0x3ad6faec,0x6c44198c,0x4a475817];function Y(e,t,n,i){for(var r,o,s,a,l,c,d,u,h,p,f,b,g,w,v,y,m,x,_,S,E,R,k,O,C,T,A=new Int32Array(16),U=new Int32Array(16),N=e[0],P=e[1],j=e[2],L=e[3],M=e[4],W=e[5],I=e[6],q=e[7],D=t[0],B=t[1],K=t[2],F=t[3],G=t[4],$=t[5],z=t[6],H=t[7],Y=0;i>=128;){for(_=0;_<16;_++)S=8*_+Y,A[_]=n[S+0]<<24|n[S+1]<<16|n[S+2]<<8|n[S+3],U[_]=n[S+4]<<24|n[S+5]<<16|n[S+6]<<8|n[S+7];for(_=0;_<80;_++)if(r=N,o=P,s=j,a=L,l=M,c=W,d=I,u=q,h=D,p=B,f=K,b=F,g=G,w=$,v=z,y=H,E=q,k=65535&(R=H),O=R>>>16,C=65535&E,T=E>>>16,E=(M>>>14|G<<18)^(M>>>18|G<<14)^(G>>>9|M<<23),k+=65535&(R=(G>>>14|M<<18)^(G>>>18|M<<14)^(M>>>9|G<<23)),O+=R>>>16,C+=65535&E,T+=E>>>16,E=M&W^~M&I,k+=65535&(R=G&$^~G&z),O+=R>>>16,C+=65535&E,T+=E>>>16,E=V[2*_],k+=65535&(R=V[2*_+1]),O+=R>>>16,C+=65535&E,T+=E>>>16,E=A[_%16],k+=65535&(R=U[_%16]),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,m=65535&C|T<<16,x=65535&k|O<<16,E=m,k=65535&(R=x),O=R>>>16,C=65535&E,T=E>>>16,E=(N>>>28|D<<4)^(D>>>2|N<<30)^(D>>>7|N<<25),k+=65535&(R=(D>>>28|N<<4)^(N>>>2|D<<30)^(N>>>7|D<<25)),O+=R>>>16,C+=65535&E,T+=E>>>16,E=N&P^N&j^P&j,k+=65535&(R=D&B^D&K^B&K),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,u=65535&C|T<<16,y=65535&k|O<<16,E=a,k=65535&(R=b),O=R>>>16,C=65535&E,T=E>>>16,E=m,k+=65535&(R=x),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,a=65535&C|T<<16,b=65535&k|O<<16,P=r,j=o,L=s,M=a,W=l,I=c,q=d,N=u,B=h,K=p,F=f,G=b,$=g,z=w,H=v,D=y,_%16==15)for(S=0;S<16;S++)E=A[S],k=65535&(R=U[S]),O=R>>>16,C=65535&E,T=E>>>16,E=A[(S+9)%16],k+=65535&(R=U[(S+9)%16]),O+=R>>>16,C+=65535&E,T+=E>>>16,E=((m=A[(S+1)%16])>>>1|(x=U[(S+1)%16])<<31)^(m>>>8|x<<24)^m>>>7,k+=65535&(R=(x>>>1|m<<31)^(x>>>8|m<<24)^(x>>>7|m<<25)),O+=R>>>16,C+=65535&E,T+=E>>>16,E=((m=A[(S+14)%16])>>>19|(x=U[(S+14)%16])<<13)^(x>>>29|m<<3)^m>>>6,k+=65535&(R=(x>>>19|m<<13)^(m>>>29|x<<3)^(x>>>6|m<<26)),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,A[S]=65535&C|T<<16,U[S]=65535&k|O<<16;E=N,k=65535&(R=D),O=R>>>16,C=65535&E,T=E>>>16,E=e[0],k+=65535&(R=t[0]),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,e[0]=N=65535&C|T<<16,t[0]=D=65535&k|O<<16,E=P,k=65535&(R=B),O=R>>>16,C=65535&E,T=E>>>16,E=e[1],k+=65535&(R=t[1]),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,e[1]=P=65535&C|T<<16,t[1]=B=65535&k|O<<16,E=j,k=65535&(R=K),O=R>>>16,C=65535&E,T=E>>>16,E=e[2],k+=65535&(R=t[2]),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,e[2]=j=65535&C|T<<16,t[2]=K=65535&k|O<<16,E=L,k=65535&(R=F),O=R>>>16,C=65535&E,T=E>>>16,E=e[3],k+=65535&(R=t[3]),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,e[3]=L=65535&C|T<<16,t[3]=F=65535&k|O<<16,E=M,k=65535&(R=G),O=R>>>16,C=65535&E,T=E>>>16,E=e[4],k+=65535&(R=t[4]),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,e[4]=M=65535&C|T<<16,t[4]=G=65535&k|O<<16,E=W,k=65535&(R=$),O=R>>>16,C=65535&E,T=E>>>16,E=e[5],k+=65535&(R=t[5]),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,e[5]=W=65535&C|T<<16,t[5]=$=65535&k|O<<16,E=I,k=65535&(R=z),O=R>>>16,C=65535&E,T=E>>>16,E=e[6],k+=65535&(R=t[6]),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,e[6]=I=65535&C|T<<16,t[6]=z=65535&k|O<<16,E=q,k=65535&(R=H),O=R>>>16,C=65535&E,T=E>>>16,E=e[7],k+=65535&(R=t[7]),O+=R>>>16,C+=65535&E,T+=E>>>16,O+=k>>>16,C+=O>>>16,T+=C>>>16,e[7]=q=65535&C|T<<16,t[7]=H=65535&k|O<<16,Y+=128,i-=128}return i}function J(e,t,n){var i,r=new Int32Array(8),o=new Int32Array(8),s=new Uint8Array(256),a=n;for(r[0]=0x6a09e667,r[1]=0xbb67ae85,r[2]=0x3c6ef372,r[3]=0xa54ff53a,r[4]=0x510e527f,r[5]=0x9b05688c,r[6]=0x1f83d9ab,r[7]=0x5be0cd19,o[0]=0xf3bcc908,o[1]=0x84caa73b,o[2]=0xfe94f82b,o[3]=0x5f1d36f1,o[4]=0xade682d1,o[5]=0x2b3e6c1f,o[6]=0xfb41bd6b,o[7]=0x137e2179,Y(r,o,t,n),n%=128,i=0;i<n;i++)s[i]=t[a-n+i];for(s[n]=128,s[(n=256-128*(n<112?1:0))-9]=0,b(s,n-8,a/0x20000000|0,a<<3),Y(r,o,s,n),i=0;i<8;i++)b(e,8*i,r[i],o[i]);return 0}function Q(e,t){var n=i(),r=i(),o=i(),s=i(),a=i(),l=i(),c=i(),d=i(),h=i();q(n,e[1],e[0]),q(h,t[1],t[0]),D(n,n,h),I(r,e[0],e[1]),I(h,t[0],t[1]),D(r,r,h),D(o,e[3],t[3]),D(o,o,u),D(s,e[2],t[2]),I(s,s,s),q(a,r,n),q(l,s,o),I(c,s,o),I(d,r,n),D(e[0],a,l),D(e[1],d,c),D(e[2],c,l),D(e[3],a,d)}function X(e,t,n){var i;for(i=0;i<4;i++)P(e[i],t[i],n)}function Z(e,t){var n=i(),r=i(),o=i();K(o,t[2]),D(n,t[0],o),D(r,t[1],o),j(e,r),e[31]^=M(n)<<7}function ee(e,t,n){var i,r;for(U(e[0],a),U(e[1],l),U(e[2],l),U(e[3],a),r=255;r>=0;--r)X(e,t,i=n[r/8|0]>>(7&r)&1),Q(t,e),Q(e,e),X(e,t,i)}function et(e,t){var n=[i(),i(),i(),i()];U(n[0],h),U(n[1],p),U(n[2],l),D(n[3],h,p),ee(e,n,t)}function en(e,t,n){var o,s=new Uint8Array(64),a=[i(),i(),i(),i()];for(n||r(t,32),J(s,t,32),s[0]&=248,s[31]&=127,s[31]|=64,et(a,s),Z(e,a),o=0;o<32;o++)t[o+32]=e[o];return 0}var ei=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function er(e,t){var n,i,r,o;for(i=63;i>=32;--i){for(n=0,r=i-32,o=i-12;r<o;++r)t[r]+=n-16*t[i]*ei[r-(i-32)],n=Math.floor((t[r]+128)/256),t[r]-=256*n;t[r]+=n,t[i]=0}for(r=0,n=0;r<32;r++)t[r]+=n-(t[31]>>4)*ei[r],n=t[r]>>8,t[r]&=255;for(r=0;r<32;r++)t[r]-=n*ei[r];for(i=0;i<32;i++)t[i+1]+=t[i]>>8,e[i]=255&t[i]}function eo(e){var t,n=new Float64Array(64);for(t=0;t<64;t++)n[t]=e[t];for(t=0;t<64;t++)e[t]=0;er(e,n)}function es(e,t,n,r){var o,s,a=new Uint8Array(64),l=new Uint8Array(64),c=new Uint8Array(64),d=new Float64Array(64),u=[i(),i(),i(),i()];for(J(a,r,32),a[0]&=248,a[31]&=127,a[31]|=64,o=0;o<n;o++)e[64+o]=t[o];for(o=0;o<32;o++)e[32+o]=a[32+o];for(J(c,e.subarray(32),n+32),eo(c),et(u,c),Z(e,u),o=32;o<64;o++)e[o]=r[o];for(J(l,e,n+64),eo(l),o=0;o<64;o++)d[o]=0;for(o=0;o<32;o++)d[o]=c[o];for(o=0;o<32;o++)for(s=0;s<32;s++)d[o+s]+=l[o]*a[s];return er(e.subarray(32),d),n+64}function ea(e,t,n,r){var o,s,c,u,h,p,b,g,w=new Uint8Array(32),y=new Uint8Array(64),m=[i(),i(),i(),i()],x=[i(),i(),i(),i()];if(n<64||(o=i(),s=i(),c=i(),u=i(),h=i(),p=i(),b=i(),(U(x[2],l),W(x[1],r),B(c,x[1]),D(u,c,d),q(c,c,x[2]),I(u,x[2],u),B(h,u),B(p,h),D(b,p,h),D(o,b,c),D(o,o,u),F(o,o),D(o,o,c),D(o,o,u),D(o,o,u),D(x[0],o,u),B(s,x[0]),D(s,s,u),L(s,c)&&D(x[0],x[0],f),B(s,x[0]),D(s,s,u),L(s,c))?-1:(M(x[0])===r[31]>>7&&q(x[0],a,x[0]),D(x[3],x[0],x[1]),0)))return -1;for(g=0;g<n;g++)e[g]=t[g];for(g=0;g<32;g++)e[g+32]=r[g];if(J(y,e,n),eo(y),ee(m,x,y),et(x,t.subarray(32)),Q(m,x),Z(w,m),n-=64,v(t,0,w,0)){for(g=0;g<n;g++)e[g]=0;return -1}for(g=0;g<n;g++)e[g]=t[g+64];return n}function el(e,t){if(32!==e.length)throw Error("bad key size");if(24!==t.length)throw Error("bad nonce size")}function ec(){for(var e=0;e<arguments.length;e++)if(!(arguments[e]instanceof Uint8Array))throw TypeError("unexpected type, use Uint8Array")}function ed(e){for(var t=0;t<e.length;t++)e[t]=0}e.lowlevel={crypto_core_hsalsa20:m,crypto_stream_xor:R,crypto_stream:E,crypto_stream_salsa20_xor:_,crypto_stream_salsa20:S,crypto_onetimeauth:O,crypto_onetimeauth_verify:C,crypto_verify_16:w,crypto_verify_32:v,crypto_secretbox:T,crypto_secretbox_open:A,crypto_scalarmult:G,crypto_scalarmult_base:$,crypto_box_beforenm:H,crypto_box_afternm:T,crypto_box:function(e,t,n,i,r,o){var s=new Uint8Array(32);return H(s,r,o),T(e,t,n,i,s)},crypto_box_open:function(e,t,n,i,r,o){var s=new Uint8Array(32);return H(s,r,o),A(e,t,n,i,s)},crypto_box_keypair:z,crypto_hash:J,crypto_sign:es,crypto_sign_keypair:en,crypto_sign_open:ea,crypto_secretbox_KEYBYTES:32,crypto_secretbox_NONCEBYTES:24,crypto_secretbox_ZEROBYTES:32,crypto_secretbox_BOXZEROBYTES:16,crypto_scalarmult_BYTES:32,crypto_scalarmult_SCALARBYTES:32,crypto_box_PUBLICKEYBYTES:32,crypto_box_SECRETKEYBYTES:32,crypto_box_BEFORENMBYTES:32,crypto_box_NONCEBYTES:24,crypto_box_ZEROBYTES:32,crypto_box_BOXZEROBYTES:16,crypto_sign_BYTES:64,crypto_sign_PUBLICKEYBYTES:32,crypto_sign_SECRETKEYBYTES:64,crypto_sign_SEEDBYTES:32,crypto_hash_BYTES:64,gf:i,D:d,L:ei,pack25519:j,unpack25519:W,M:D,A:I,S:B,Z:q,pow2523:F,add:Q,set25519:U,modL:er,scalarmult:ee,scalarbase:et},e.randomBytes=function(e){var t=new Uint8Array(e);return r(t,e),t},e.secretbox=function(e,t,n){ec(e,t,n),el(n,t);for(var i=new Uint8Array(32+e.length),r=new Uint8Array(i.length),o=0;o<e.length;o++)i[o+32]=e[o];return T(r,i,i.length,t,n),r.subarray(16)},e.secretbox.open=function(e,t,n){ec(e,t,n),el(n,t);for(var i=new Uint8Array(16+e.length),r=new Uint8Array(i.length),o=0;o<e.length;o++)i[o+16]=e[o];return i.length<32||0!==A(r,i,i.length,t,n)?null:r.subarray(32)},e.secretbox.keyLength=32,e.secretbox.nonceLength=24,e.secretbox.overheadLength=16,e.scalarMult=function(e,t){if(ec(e,t),32!==e.length)throw Error("bad n size");if(32!==t.length)throw Error("bad p size");var n=new Uint8Array(32);return G(n,e,t),n},e.scalarMult.base=function(e){if(ec(e),32!==e.length)throw Error("bad n size");var t=new Uint8Array(32);return $(t,e),t},e.scalarMult.scalarLength=32,e.scalarMult.groupElementLength=32,e.box=function(t,n,i,r){var o=e.box.before(i,r);return e.secretbox(t,n,o)},e.box.before=function(e,t){ec(e,t),function(e,t){if(32!==e.length)throw Error("bad public key size");if(32!==t.length)throw Error("bad secret key size")}(e,t);var n=new Uint8Array(32);return H(n,e,t),n},e.box.after=e.secretbox,e.box.open=function(t,n,i,r){var o=e.box.before(i,r);return e.secretbox.open(t,n,o)},e.box.open.after=e.secretbox.open,e.box.keyPair=function(){var e=new Uint8Array(32),t=new Uint8Array(32);return z(e,t),{publicKey:e,secretKey:t}},e.box.keyPair.fromSecretKey=function(e){if(ec(e),32!==e.length)throw Error("bad secret key size");var t=new Uint8Array(32);return $(t,e),{publicKey:t,secretKey:new Uint8Array(e)}},e.box.publicKeyLength=32,e.box.secretKeyLength=32,e.box.sharedKeyLength=32,e.box.nonceLength=24,e.box.overheadLength=e.secretbox.overheadLength,e.sign=function(e,t){if(ec(e,t),64!==t.length)throw Error("bad secret key size");var n=new Uint8Array(64+e.length);return es(n,e,e.length,t),n},e.sign.open=function(e,t){if(ec(e,t),32!==t.length)throw Error("bad public key size");var n=new Uint8Array(e.length),i=ea(n,e,e.length,t);if(i<0)return null;for(var r=new Uint8Array(i),o=0;o<r.length;o++)r[o]=n[o];return r},e.sign.detached=function(t,n){for(var i=e.sign(t,n),r=new Uint8Array(64),o=0;o<r.length;o++)r[o]=i[o];return r},e.sign.detached.verify=function(e,t,n){if(ec(e,t,n),64!==t.length)throw Error("bad signature size");if(32!==n.length)throw Error("bad public key size");var i,r=new Uint8Array(64+e.length),o=new Uint8Array(64+e.length);for(i=0;i<64;i++)r[i]=t[i];for(i=0;i<e.length;i++)r[i+64]=e[i];return ea(o,r,r.length,n)>=0},e.sign.keyPair=function(){var e=new Uint8Array(32),t=new Uint8Array(64);return en(e,t),{publicKey:e,secretKey:t}},e.sign.keyPair.fromSecretKey=function(e){if(ec(e),64!==e.length)throw Error("bad secret key size");for(var t=new Uint8Array(32),n=0;n<t.length;n++)t[n]=e[32+n];return{publicKey:t,secretKey:new Uint8Array(e)}},e.sign.keyPair.fromSeed=function(e){if(ec(e),32!==e.length)throw Error("bad seed size");for(var t=new Uint8Array(32),n=new Uint8Array(64),i=0;i<32;i++)n[i]=e[i];return en(t,n,!0),{publicKey:t,secretKey:n}},e.sign.publicKeyLength=32,e.sign.secretKeyLength=64,e.sign.seedLength=32,e.sign.signatureLength=64,e.hash=function(e){ec(e);var t=new Uint8Array(64);return J(t,e,e.length),t},e.hash.hashLength=64,e.verify=function(e,t){return ec(e,t),0!==e.length&&0!==t.length&&e.length===t.length&&0===g(e,0,t,0,e.length)},e.setPRNG=function(e){r=e},(t="undefined"!=typeof self?self.crypto||self.msCrypto:null)&&t.getRandomValues?e.setPRNG(function(e,n){var i,r=new Uint8Array(n);for(i=0;i<n;i+=65536)t.getRandomValues(r.subarray(i,i+Math.min(n-i,65536)));for(i=0;i<n;i++)e[i]=r[i];ed(r)}):(t=n(1281))&&t.randomBytes&&e.setPRNG(function(e,n){var i,r=t.randomBytes(n);for(i=0;i<n;i++)e[i]=r[i];ed(r)})}(e.exports?e.exports:self.nacl=self.nacl||{})},5720:function(e,t,n){var i;!function(r,o){"use strict";var s="function",a="undefined",l="object",c="string",d="major",u="model",h="name",p="type",f="vendor",b="version",g="architecture",w="console",v="mobile",y="tablet",m="smarttv",x="wearable",_="embedded",S="Amazon",E="Apple",R="ASUS",k="BlackBerry",O="Browser",C="Chrome",T="Firefox",A="Google",U="Huawei",N="Microsoft",P="Motorola",j="Opera",L="Samsung",M="Sharp",W="Sony",I="Xiaomi",q="Zebra",D="Facebook",B="Chromium OS",K="Mac OS",F=" Browser",G=function(e,t){var n={};for(var i in e)t[i]&&t[i].length%2==0?n[i]=t[i].concat(e[i]):n[i]=e[i];return n},$=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},z=function(e,t){return typeof e===c&&-1!==H(t).indexOf(H(e))},H=function(e){return e.toLowerCase()},V=function(e,t){if(typeof e===c)return e=e.replace(/^\s\s*/,""),typeof t===a?e:e.substring(0,500)},Y=function(e,t){for(var n,i,r,a,c,d,u=0;u<t.length&&!c;){var h=t[u],p=t[u+1];for(n=i=0;n<h.length&&!c&&h[n];)if(c=h[n++].exec(e))for(r=0;r<p.length;r++)d=c[++i],typeof(a=p[r])===l&&a.length>0?2===a.length?typeof a[1]==s?this[a[0]]=a[1].call(this,d):this[a[0]]=a[1]:3===a.length?typeof a[1]!==s||a[1].exec&&a[1].test?this[a[0]]=d?d.replace(a[1],a[2]):void 0:this[a[0]]=d?a[1].call(this,d,a[2]):void 0:4===a.length&&(this[a[0]]=d?a[3].call(this,d.replace(a[1],a[2])):void 0):this[a]=d||o;u+=2}},J=function(e,t){for(var n in t)if(typeof t[n]===l&&t[n].length>0){for(var i=0;i<t[n].length;i++)if(z(t[n][i],e))return"?"===n?o:n}else if(z(t[n],e))return"?"===n?o:n;return t.hasOwnProperty("*")?t["*"]:e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2","8.1":"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},X={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[b,[h,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[b,[h,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[h,b],[/opios[\/ ]+([\w\.]+)/i],[b,[h,j+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[b,[h,j+" GX"]],[/\bopr\/([\w\.]+)/i],[b,[h,j]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[b,[h,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[b,[h,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[h,b],[/quark(?:pc)?\/([-\w\.]+)/i],[b,[h,"Quark"]],[/\bddg\/([\w\.]+)/i],[b,[h,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[b,[h,"UC"+O]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[b,[h,"WeChat"]],[/konqueror\/([\w\.]+)/i],[b,[h,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[b,[h,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[b,[h,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[b,[h,"Smart Lenovo "+O]],[/(avast|avg)\/([\w\.]+)/i],[[h,/(.+)/,"$1 Secure "+O],b],[/\bfocus\/([\w\.]+)/i],[b,[h,T+" Focus"]],[/\bopt\/([\w\.]+)/i],[b,[h,j+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[b,[h,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[b,[h,"Dolphin"]],[/coast\/([\w\.]+)/i],[b,[h,j+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[b,[h,"MIUI"+F]],[/fxios\/([\w\.-]+)/i],[b,[h,T]],[/\bqihoobrowser\/?([\w\.]*)/i],[b,[h,"360"]],[/\b(qq)\/([\w\.]+)/i],[[h,/(.+)/,"$1Browser"],b],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[h,/(.+)/,"$1"+F],b],[/samsungbrowser\/([\w\.]+)/i],[b,[h,L+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[b,[h,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[h,"Sogou Mobile"],b],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[h,b],[/(lbbrowser|rekonq)/i,/\[(linkedin)app\]/i],[h],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[b,h],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[h,D],b],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[h,b],[/\bgsa\/([\w\.]+) .*safari\//i],[b,[h,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[b,[h,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[b,[h,C+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[h,C+" WebView"],b],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[b,[h,"Android "+O]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[h,b],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[b,[h,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[b,h],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[h,[b,J,{"1.0":"/8","1.2":"/1","1.3":"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[h,b],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[h,"Netscape"],b],[/(wolvic|librewolf)\/([\w\.]+)/i],[h,b],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[b,[h,T+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[h,[b,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[h,[b,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,H]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,"",H]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,H]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[u,[f,L],[p,y]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[u,[f,L],[p,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[u,[f,E],[p,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[u,[f,E],[p,y]],[/(macintosh);/i],[u,[f,E]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[u,[f,M],[p,v]],[/(?:honor)([-\w ]+)[;\)]/i],[u,[f,"Honor"],[p,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[u,[f,U],[p,y]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[u,[f,U],[p,v]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[u,/_/g," "],[f,I],[p,v]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[u,/_/g," "],[f,I],[p,y]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[u,[f,"OPPO"],[p,v]],[/\b(opd2\d{3}a?) bui/i],[u,[f,"OPPO"],[p,y]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[u,[f,"Vivo"],[p,v]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[u,[f,"Realme"],[p,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[u,[f,P],[p,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[u,[f,P],[p,y]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[u,[f,"LG"],[p,y]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[u,[f,"LG"],[p,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[u,[f,"Lenovo"],[p,y]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[u,/_/g," "],[f,"Nokia"],[p,v]],[/(pixel c)\b/i],[u,[f,A],[p,y]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[u,[f,A],[p,v]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[u,[f,W],[p,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[u,"Xperia Tablet"],[f,W],[p,y]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[u,[f,"OnePlus"],[p,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[u,[f,S],[p,y]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[u,/(.+)/g,"Fire Phone $1"],[f,S],[p,v]],[/(playbook);[-\w\),; ]+(rim)/i],[u,f,[p,y]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[u,[f,k],[p,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[u,[f,R],[p,y]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[u,[f,R],[p,v]],[/(nexus 9)/i],[u,[f,"HTC"],[p,y]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[f,[u,/_/g," "],[p,v]],[/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i],[u,[f,"TCL"],[p,y]],[/(itel) ((\w+))/i],[[f,H],u,[p,J,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[u,[f,"Acer"],[p,y]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[u,[f,"Meizu"],[p,v]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[u,[f,"Ulefone"],[p,v]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[u,[f,"Energizer"],[p,v]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[u,[f,"Cat"],[p,v]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[u,[f,"Smartfren"],[p,v]],[/droid.+; (a(?:015|06[35]|142p?))/i],[u,[f,"Nothing"],[p,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[f,u,[p,v]],[/(imo) (tab \w+)/i,/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[f,u,[p,y]],[/(surface duo)/i],[u,[f,N],[p,y]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[u,[f,"Fairphone"],[p,v]],[/(u304aa)/i],[u,[f,"AT&T"],[p,v]],[/\bsie-(\w*)/i],[u,[f,"Siemens"],[p,v]],[/\b(rct\w+) b/i],[u,[f,"RCA"],[p,y]],[/\b(venue[\d ]{2,7}) b/i],[u,[f,"Dell"],[p,y]],[/\b(q(?:mv|ta)\w+) b/i],[u,[f,"Verizon"],[p,y]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[u,[f,"Barnes & Noble"],[p,y]],[/\b(tm\d{3}\w+) b/i],[u,[f,"NuVision"],[p,y]],[/\b(k88) b/i],[u,[f,"ZTE"],[p,y]],[/\b(nx\d{3}j) b/i],[u,[f,"ZTE"],[p,v]],[/\b(gen\d{3}) b.+49h/i],[u,[f,"Swiss"],[p,v]],[/\b(zur\d{3}) b/i],[u,[f,"Swiss"],[p,y]],[/\b((zeki)?tb.*\b) b/i],[u,[f,"Zeki"],[p,y]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[f,"Dragon Touch"],u,[p,y]],[/\b(ns-?\w{0,9}) b/i],[u,[f,"Insignia"],[p,y]],[/\b((nxa|next)-?\w{0,9}) b/i],[u,[f,"NextBook"],[p,y]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[f,"Voice"],u,[p,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[f,"LvTel"],u,[p,v]],[/\b(ph-1) /i],[u,[f,"Essential"],[p,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[u,[f,"Envizen"],[p,y]],[/\b(trio[-\w\. ]+) b/i],[u,[f,"MachSpeed"],[p,y]],[/\btu_(1491) b/i],[u,[f,"Rotor"],[p,y]],[/(shield[\w ]+) b/i],[u,[f,"Nvidia"],[p,y]],[/(sprint) (\w+)/i],[f,u,[p,v]],[/(kin\.[onetw]{3})/i],[[u,/\./g," "],[f,N],[p,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[u,[f,q],[p,y]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[u,[f,q],[p,v]],[/smart-tv.+(samsung)/i],[f,[p,m]],[/hbbtv.+maple;(\d+)/i],[[u,/^/,"SmartTV"],[f,L],[p,m]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[f,"LG"],[p,m]],[/(apple) ?tv/i],[f,[u,E+" TV"],[p,m]],[/crkey/i],[[u,C+"cast"],[f,A],[p,m]],[/droid.+aft(\w+)( bui|\))/i],[u,[f,S],[p,m]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[u,[f,M],[p,m]],[/(bravia[\w ]+)( bui|\))/i],[u,[f,W],[p,m]],[/(mitv-\w{5}) bui/i],[u,[f,I],[p,m]],[/Hbbtv.*(technisat) (.*);/i],[f,u,[p,m]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[f,V],[u,V],[p,m]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,m]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[f,u,[p,w]],[/droid.+; (shield) bui/i],[u,[f,"Nvidia"],[p,w]],[/(playstation [345portablevi]+)/i],[u,[f,W],[p,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[u,[f,N],[p,w]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[u,[f,L],[p,x]],[/((pebble))app/i],[f,u,[p,x]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[u,[f,E],[p,x]],[/droid.+; (glass) \d/i],[u,[f,A],[p,x]],[/droid.+; (wt63?0{2,3})\)/i],[u,[f,q],[p,x]],[/droid.+; (glass) \d/i],[u,[f,A],[p,x]],[/(pico) (4|neo3(?: link|pro)?)/i],[f,u,[p,x]],[/; (quest( \d| pro)?)/i],[u,[f,D],[p,x]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[f,[p,_]],[/(aeobc)\b/i],[u,[f,S],[p,_]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[u,[p,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[u,[p,y]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,y]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,v]],[/(android[-\w\. ]{0,9});.+buil/i],[u,[f,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[b,[h,"EdgeHTML"]],[/(arkweb)\/([\w\.]+)/i],[h,b],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[b,[h,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[h,b],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[b,h]],os:[[/microsoft (windows) (vista|xp)/i],[h,b],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[h,[b,J,Q]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[b,J,Q],[h,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[b,/_/g,"."],[h,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[h,K],[b,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[b,h],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[h,b],[/\(bb(10);/i],[b,[h,k]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[b,[h,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[b,[h,T+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[b,[h,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[b,[h,"watchOS"]],[/crkey\/([\d\.]+)/i],[b,[h,C+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[h,B],b],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[h,b],[/(sunos) ?([\w\.\d]*)/i],[[h,"Solaris"],b],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[h,b]]},Z=function(e,t){if(typeof e===l&&(t=e,e=o),!(this instanceof Z))return new Z(e,t).getResult();var n=typeof r!==a&&r.navigator?r.navigator:o,i=e||(n&&n.userAgent?n.userAgent:""),w=n&&n.userAgentData?n.userAgentData:o,m=t?G(X,t):X,x=n&&n.userAgent==i;return this.getBrowser=function(){var e,t={};return t[h]=o,t[b]=o,Y.call(t,i,m.browser),t[d]=typeof(e=t[b])===c?e.replace(/[^\d\.]/g,"").split(".")[0]:o,x&&n&&n.brave&&typeof n.brave.isBrave==s&&(t[h]="Brave"),t},this.getCPU=function(){var e={};return e[g]=o,Y.call(e,i,m.cpu),e},this.getDevice=function(){var e={};return e[f]=o,e[u]=o,e[p]=o,Y.call(e,i,m.device),x&&!e[p]&&w&&w.mobile&&(e[p]=v),x&&"Macintosh"==e[u]&&n&&typeof n.standalone!==a&&n.maxTouchPoints&&n.maxTouchPoints>2&&(e[u]="iPad",e[p]=y),e},this.getEngine=function(){var e={};return e[h]=o,e[b]=o,Y.call(e,i,m.engine),e},this.getOS=function(){var e={};return e[h]=o,e[b]=o,Y.call(e,i,m.os),x&&!e[h]&&w&&w.platform&&"Unknown"!=w.platform&&(e[h]=w.platform.replace(/chrome os/i,B).replace(/macos/i,K)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===c&&e.length>500?V(e,500):e,this},this.setUA(i),this};Z.VERSION="1.0.40",Z.BROWSER=$([h,b,d]),Z.CPU=$([g]),Z.DEVICE=$([u,f,p,w,v,m,y,x,_]),Z.ENGINE=Z.OS=$([h,b]),typeof t!==a?(e.exports&&(t=e.exports=Z),t.UAParser=Z):n.amdO?o!==(i=(function(){return Z}).call(t,n,t,e))&&(e.exports=i):typeof r!==a&&(r.UAParser=Z);var ee=typeof r!==a&&(r.jQuery||r.Zepto);if(ee&&!ee.ua){var et=new Z;ee.ua=et.getResult(),ee.ua.get=function(){return et.getUA()},ee.ua.set=function(e){et.setUA(e);var t=et.getResult();for(var n in t)ee.ua[n]=t[n]}}}("object"==typeof window?window:this)},4617:(e,t)=>{var n;!function(){"use strict";var i={}.hasOwnProperty;function r(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=o(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return r.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)i.call(e,n)&&e[n]&&(t=o(t,n));return t}(n)))}return e}function o(e,t){return t?e?e+" "+t:e+t:e}e.exports?(r.default=r,e.exports=r):void 0!==(n=(function(){return r}).apply(t,[]))&&(e.exports=n)}()},5984:(e,t,n)=>{"use strict";n.d(t,{m_:()=>eP,S5:()=>l,GE:()=>eL,K3:()=>v,iA:()=>k,kT:()=>R,_0:()=>ep,sQ:()=>em,$e:()=>ex,NQ:()=>eS,Mx:()=>eE,j9:()=>e_,Es:()=>ey,q:()=>eT,lp:()=>eA,Dg:()=>eU,dQ:()=>eN,Gz:()=>eb,m5:()=>eg,aV:()=>ek,TB:()=>eO,VA:()=>eC,BH:()=>ew,fB:()=>H,Vb:()=>z,iw:()=>ec,Ee:()=>el,ip:()=>ed,Sq:()=>eM});var i,r,o,s,a,l,c=n(8254),d=n(9089);n(2818),function(e){e[e.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",e[e.BAD_REQUEST_ERROR=1]="BAD_REQUEST_ERROR",e[e.MANIFEST_NOT_FOUND_ERROR=2]="MANIFEST_NOT_FOUND_ERROR",e[e.MANIFEST_CONTENT_ERROR=3]="MANIFEST_CONTENT_ERROR",e[e.UNKNOWN_APP_ERROR=100]="UNKNOWN_APP_ERROR",e[e.USER_REJECTS_ERROR=300]="USER_REJECTS_ERROR",e[e.METHOD_NOT_SUPPORTED=400]="METHOD_NOT_SUPPORTED"}(i||(i={})),function(e){e[e.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",e[e.METHOD_NOT_SUPPORTED=400]="METHOD_NOT_SUPPORTED"}(r||(r={})),function(e){e[e.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",e[e.BAD_REQUEST_ERROR=1]="BAD_REQUEST_ERROR",e[e.UNKNOWN_APP_ERROR=100]="UNKNOWN_APP_ERROR",e[e.USER_REJECTS_ERROR=300]="USER_REJECTS_ERROR",e[e.METHOD_NOT_SUPPORTED=400]="METHOD_NOT_SUPPORTED"}(o||(o={})),function(e){e[e.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",e[e.BAD_REQUEST_ERROR=1]="BAD_REQUEST_ERROR",e[e.UNKNOWN_APP_ERROR=100]="UNKNOWN_APP_ERROR",e[e.USER_REJECTS_ERROR=300]="USER_REJECTS_ERROR",e[e.METHOD_NOT_SUPPORTED=400]="METHOD_NOT_SUPPORTED"}(s||(s={})),function(e){e[e.UNKNOWN_ERROR=0]="UNKNOWN_ERROR",e[e.BAD_REQUEST_ERROR=1]="BAD_REQUEST_ERROR",e[e.UNKNOWN_APP_ERROR=100]="UNKNOWN_APP_ERROR",e[e.METHOD_NOT_SUPPORTED=400]="METHOD_NOT_SUPPORTED"}(a||(a={})),function(e){e.MAINNET="-239",e.TESTNET="-3"}(l||(l={}));let u={encode:function(e,t=!1){let n;return e instanceof Uint8Array?n=e:("string"!=typeof e&&(e=JSON.stringify(e)),n=c.decodeUTF8(e)),function(e,t){let n=c.encodeBase64(e);return t?encodeURIComponent(n):n}(n,t)},decode:function(e,t=!1){var n;let i=(n=e,t&&(n=decodeURIComponent(n)),c.decodeBase64(n));return{toString:()=>c.encodeUTF8(i),toObject(){try{return JSON.parse(c.encodeUTF8(i))}catch(e){return null}},toUint8Array:()=>i}}};function h(e){let t="";return e.forEach(e=>{t+=("0"+(255&e).toString(16)).slice(-2)}),t}function p(e){if(e.length%2!=0)throw Error(`Cannot convert ${e} to bytesArray`);let t=new Uint8Array(e.length/2);for(let n=0;n<e.length;n+=2)t[n/2]=parseInt(e.slice(n,n+2),16);return t}class f{constructor(e){this.nonceLength=24,this.keyPair=e?this.createKeypairFromString(e):this.createKeypair(),this.sessionId=h(this.keyPair.publicKey)}createKeypair(){return d.box.keyPair()}createKeypairFromString(e){return{publicKey:p(e.publicKey),secretKey:p(e.secretKey)}}createNonce(){return d.randomBytes(this.nonceLength)}encrypt(e,t){let n=new TextEncoder().encode(e),i=this.createNonce(),r=d.box(n,i,t,this.keyPair.secretKey);return function(e,t){let n=new Uint8Array(e.length+t.length);return n.set(e),n.set(t,e.length),n}(i,r)}decrypt(e,t){let[n,i]=function(e,t){if(t>=e.length)throw Error("Index is out of buffer");return[e.slice(0,t),e.slice(t)]}(e,this.nonceLength),r=d.box.open(i,n,t,this.keyPair.secretKey);if(!r)throw Error(`Decryption error: 
 message: ${e.toString()} 
 sender pubkey: ${t.toString()} 
 keypair pubkey: ${this.keyPair.publicKey.toString()} 
 keypair secretkey: ${this.keyPair.secretKey.toString()}`);return new TextDecoder().decode(r)}stringifyKeypair(){return{publicKey:h(this.keyPair.publicKey),secretKey:h(this.keyPair.secretKey)}}}var b=n(2818);function g(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)0>t.indexOf(i[r])&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(n[i[r]]=e[i[r]]);return n}function w(e,t,n,i){return new(n||(n=Promise))(function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}l((i=i.apply(e,t||[])).next())})}class v extends Error{constructor(e,t){super(e,t),this.message=`${v.prefix} ${this.constructor.name}${this.info?": "+this.info:""}${e?"\n"+e:""}`,Object.setPrototypeOf(this,v.prototype)}get info(){return""}}v.prefix="[TON_CONNECT_SDK_ERROR]";class y extends v{get info(){return"Passed DappMetadata is in incorrect format."}constructor(...e){super(...e),Object.setPrototypeOf(this,y.prototype)}}class m extends v{get info(){return"Passed `tonconnect-manifest.json` contains errors. Check format of your manifest. See more https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest"}constructor(...e){super(...e),Object.setPrototypeOf(this,m.prototype)}}class x extends v{get info(){return"Manifest not found. Make sure you added `tonconnect-manifest.json` to the root of your app or passed correct manifestUrl. See more https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest"}constructor(...e){super(...e),Object.setPrototypeOf(this,x.prototype)}}class _ extends v{get info(){return"Wallet connection called but wallet already connected. To avoid the error, disconnect the wallet before doing a new connection."}constructor(...e){super(...e),Object.setPrototypeOf(this,_.prototype)}}class S extends v{get info(){return"Send transaction or other protocol methods called while wallet is not connected."}constructor(...e){super(...e),Object.setPrototypeOf(this,S.prototype)}}class E extends v{get info(){return"There is an attempt to connect to the injected wallet while it is not exists in the webpage."}constructor(...e){super(...e),Object.setPrototypeOf(this,E.prototype)}}class R extends v{get info(){return"Wallet doesn't support requested feature method."}constructor(e,t){super(e,t),Object.setPrototypeOf(this,R.prototype)}}class k extends v{get info(){return"Missing required features. You need to update your wallet."}constructor(e,t){super(e,t),Object.setPrototypeOf(this,k.prototype)}}class O extends v{get info(){return"User rejects the action in the wallet."}constructor(...e){super(...e),Object.setPrototypeOf(this,O.prototype)}}class C extends v{get info(){return"Request to the wallet contains errors."}constructor(...e){super(...e),Object.setPrototypeOf(this,C.prototype)}}class T extends v{get info(){return"App tries to send rpc request to the injected wallet while not connected."}constructor(...e){super(...e),Object.setPrototypeOf(this,T.prototype)}}class A extends v{get info(){return"An error occurred while fetching the wallets list."}constructor(...e){super(...e),Object.setPrototypeOf(this,A.prototype)}}class U extends v{get info(){return"Passed address is in incorrect format."}constructor(...e){super(...e),Object.setPrototypeOf(this,U.prototype)}}class N extends v{get info(){return"Passed hex is in incorrect format."}constructor(...e){super(...e),Object.setPrototypeOf(this,N.prototype)}}class P extends v{constructor(...e){super(...e),Object.setPrototypeOf(this,P.prototype)}}let j={[i.UNKNOWN_ERROR]:P,[i.USER_REJECTS_ERROR]:O,[i.BAD_REQUEST_ERROR]:C,[i.UNKNOWN_APP_ERROR]:T,[i.MANIFEST_NOT_FOUND_ERROR]:x,[i.MANIFEST_CONTENT_ERROR]:m};class L{parseError(e){let t=P;return e.code in j&&(t=j[e.code]||P),new t(e.message)}}let M=new L;class W{isError(e){return"error"in e}}let I={[o.UNKNOWN_ERROR]:P,[o.USER_REJECTS_ERROR]:O,[o.BAD_REQUEST_ERROR]:C,[o.UNKNOWN_APP_ERROR]:T};class q extends W{convertToRpcRequest(e){return{method:"sendTransaction",params:[JSON.stringify(e)]}}parseAndThrowError(e){let t=P;throw e.error.code in I&&(t=I[e.error.code]||P),new t(e.error.message)}convertFromRpcResponse(e){return{boc:e.result}}}let D=new q,B={[s.UNKNOWN_ERROR]:P,[s.USER_REJECTS_ERROR]:O,[s.BAD_REQUEST_ERROR]:C,[s.UNKNOWN_APP_ERROR]:T};class K extends W{convertToRpcRequest(e){return{method:"signData",params:[JSON.stringify(e)]}}parseAndThrowError(e){let t=P;throw e.error.code in B&&(t=B[e.error.code]||P),new t(e.error.message)}convertFromRpcResponse(e){return e.result}}let F=new K;class G{constructor(e,t){this.storage=e,this.storeKey="ton-connect-storage_http-bridge-gateway::"+t}storeLastEventId(e){return w(this,void 0,void 0,function*(){return this.storage.setItem(this.storeKey,e)})}removeLastEventId(){return w(this,void 0,void 0,function*(){return this.storage.removeItem(this.storeKey)})}getLastEventId(){return w(this,void 0,void 0,function*(){return(yield this.storage.getItem(this.storeKey))||null})}}function $(e,t){return("/"===e.slice(-1)?e.slice(0,-1):e)+"/"+t}function z(e){if(!e)return!1;let t=new URL(e);return"tg:"===t.protocol||"t.me"===t.hostname}function H(e){return e.replaceAll(".","%2E").replaceAll("-","%2D").replaceAll("_","%5F").replaceAll("&","-").replaceAll("=","__").replaceAll("%","--")}function V(e,t){return w(this,void 0,void 0,function*(){return new Promise((n,i)=>{var r,o;if(null===(r=null==t?void 0:t.signal)||void 0===r?void 0:r.aborted){i(new v("Delay aborted"));return}let s=setTimeout(()=>n(),e);null===(o=null==t?void 0:t.signal)||void 0===o||o.addEventListener("abort",()=>{clearTimeout(s),i(new v("Delay aborted"))})})})}function Y(e){let t=new AbortController;return(null==e?void 0:e.aborted)?t.abort():null==e||e.addEventListener("abort",()=>t.abort(),{once:!0}),t}function J(e,t){var n,i;return w(this,void 0,void 0,function*(){let r;let o=null!==(n=null==t?void 0:t.attempts)&&void 0!==n?n:10,s=null!==(i=null==t?void 0:t.delayMs)&&void 0!==i?i:200,a=Y(null==t?void 0:t.signal);if("function"!=typeof e)throw new v(`Expected a function, got ${typeof e}`);let l=0;for(;l<o;){if(a.signal.aborted)throw new v(`Aborted after attempts ${l}`);try{return yield e({signal:a.signal})}catch(e){r=e,++l<o&&(yield V(s))}}throw r})}function Q(...e){try{console.debug("[TON_CONNECT_SDK]",...e)}catch(e){}}function X(...e){try{console.error("[TON_CONNECT_SDK]",...e)}catch(e){}}class Z{constructor(e,t,n,i,r){this.bridgeUrl=t,this.sessionId=n,this.listener=i,this.errorsListener=r,this.ssePath="events",this.postPath="message",this.heartbeatMessage="heartbeat",this.defaultTtl=300,this.defaultReconnectDelay=2e3,this.defaultResendDelay=5e3,this.eventSource=function(e,t){let n=null,i=null,r=null,o=null,s=null,a=(a,...l)=>w(this,void 0,void 0,function*(){if(o=null!=a?a:null,null==s||s.abort(),(s=Y(a)).signal.aborted)throw new v("Resource creation was aborted");i=null!=l?l:null;let c=e(s.signal,...l);r=c;let d=yield c;if(r!==c&&d!==n)throw yield t(d),new v("Resource creation was aborted by a new resource creation");return n=d});return{create:a,current:()=>null!=n?n:null,dispose:()=>w(this,void 0,void 0,function*(){try{let e=n;n=null;let i=r;r=null;try{null==s||s.abort()}catch(e){}yield Promise.allSettled([e?t(e):Promise.resolve(),i?t((yield i)):Promise.resolve()])}catch(e){}}),recreate:e=>w(this,void 0,void 0,function*(){let t=n,s=r,l=i,c=o;if(yield V(e),t===n&&s===r&&l===i&&c===o)return yield a(o,...null!=l?l:[]);throw new v("Resource recreation was aborted by a new resource creation")})}}((e,t)=>w(this,void 0,void 0,function*(){let n={bridgeUrl:this.bridgeUrl,ssePath:this.ssePath,sessionId:this.sessionId,bridgeGatewayStorage:this.bridgeGatewayStorage,errorHandler:this.errorsHandler.bind(this),messageHandler:this.messagesHandler.bind(this),signal:e,openingDeadlineMS:t};return yield function(e){return w(this,void 0,void 0,function*(){return yield function(e,t){let n=null==t?void 0:t.timeout,i=Y(null==t?void 0:t.signal);return new Promise((t,r)=>w(this,void 0,void 0,function*(){let o;if(i.signal.aborted){r(new v("Operation aborted"));return}void 0!==n&&(o=setTimeout(()=>{i.abort(),r(new v(`Timeout after ${n}ms`))},n)),i.signal.addEventListener("abort",()=>{clearTimeout(o),r(new v("Operation aborted"))},{once:!0});let s={timeout:n,abort:i.signal};yield e((...e)=>{clearTimeout(o),t(...e)},()=>{clearTimeout(o),r()},s)}))}((t,n,i)=>w(this,void 0,void 0,function*(){var r;let o=Y(i.signal).signal;if(o.aborted){n(new v("Bridge connection aborted"));return}let s=new URL($(e.bridgeUrl,e.ssePath));s.searchParams.append("client_id",e.sessionId);let a=yield e.bridgeGatewayStorage.getLastEventId();if(a&&s.searchParams.append("last_event_id",a),o.aborted){n(new v("Bridge connection aborted"));return}let l=new EventSource(s.toString());l.onerror=i=>w(this,void 0,void 0,function*(){if(o.aborted){l.close(),n(new v("Bridge connection aborted"));return}try{let n=yield e.errorHandler(l,i);n!==l&&l.close(),n&&n!==l&&t(n)}catch(e){l.close(),n(e)}}),l.onopen=()=>{if(o.aborted){l.close(),n(new v("Bridge connection aborted"));return}t(l)},l.onmessage=t=>{if(o.aborted){l.close(),n(new v("Bridge connection aborted"));return}e.messageHandler(t)},null===(r=e.signal)||void 0===r||r.addEventListener("abort",()=>{l.close(),n(new v("Bridge connection aborted"))})}),{timeout:e.openingDeadlineMS,signal:e.signal})})}(n)}),e=>w(this,void 0,void 0,function*(){e.close()})),this.bridgeGatewayStorage=new G(e,t)}get isReady(){let e=this.eventSource.current();return(null==e?void 0:e.readyState)===EventSource.OPEN}get isClosed(){let e=this.eventSource.current();return(null==e?void 0:e.readyState)!==EventSource.OPEN}get isConnecting(){let e=this.eventSource.current();return(null==e?void 0:e.readyState)===EventSource.CONNECTING}registerSession(e){return w(this,void 0,void 0,function*(){yield this.eventSource.create(null==e?void 0:e.signal,null==e?void 0:e.openingDeadlineMS)})}send(e,t,n,i){var r;return w(this,void 0,void 0,function*(){let o={};"number"==typeof i?o.ttl=i:(o.ttl=null==i?void 0:i.ttl,o.signal=null==i?void 0:i.signal,o.attempts=null==i?void 0:i.attempts);let s=new URL($(this.bridgeUrl,this.postPath));s.searchParams.append("client_id",this.sessionId),s.searchParams.append("to",t),s.searchParams.append("ttl",((null==o?void 0:o.ttl)||this.defaultTtl).toString()),s.searchParams.append("topic",n);let a=u.encode(e);yield J(e=>w(this,void 0,void 0,function*(){let t=yield this.post(s,a,e.signal);if(!t.ok)throw new v(`Bridge send failed, status ${t.status}`)}),{attempts:null!==(r=null==o?void 0:o.attempts)&&void 0!==r?r:Number.MAX_SAFE_INTEGER,delayMs:this.defaultResendDelay,signal:null==o?void 0:o.signal})})}pause(){this.eventSource.dispose().catch(e=>X(`Bridge pause failed, ${e}`))}unPause(){return w(this,void 0,void 0,function*(){yield this.eventSource.recreate(0)})}close(){return w(this,void 0,void 0,function*(){yield this.eventSource.dispose().catch(e=>X(`Bridge close failed, ${e}`))})}setListener(e){this.listener=e}setErrorsListener(e){this.errorsListener=e}post(e,t,n){return w(this,void 0,void 0,function*(){let i=yield fetch(e,{method:"post",body:t,signal:n});if(!i.ok)throw new v(`Bridge send failed, status ${i.status}`);return i})}errorsHandler(e,t){return w(this,void 0,void 0,function*(){if(this.isConnecting)throw e.close(),new v("Bridge error, failed to connect");if(this.isReady){try{this.errorsListener(t)}catch(e){}return}if(this.isClosed)return e.close(),Q(`Bridge reconnecting, ${this.defaultReconnectDelay}ms delay`),yield this.eventSource.recreate(this.defaultReconnectDelay);throw new v("Bridge error, unknown state")})}messagesHandler(e){return w(this,void 0,void 0,function*(){let t;if(e.data!==this.heartbeatMessage&&(yield this.bridgeGatewayStorage.storeLastEventId(e.lastEventId),!this.isClosed)){try{t=JSON.parse(e.data)}catch(t){throw new v(`Bridge message parse failed, message ${e.data}`)}this.listener(t)}})}}function ee(e){return!("connectEvent"in e)}class et{constructor(e){this.storage=e,this.storeKey="ton-connect-storage_bridge-connection"}storeConnection(e){return w(this,void 0,void 0,function*(){if("injected"===e.type)return this.storage.setItem(this.storeKey,JSON.stringify(e));if(!ee(e)){let t={sessionKeyPair:e.session.sessionCrypto.stringifyKeypair(),walletPublicKey:e.session.walletPublicKey,bridgeUrl:e.session.bridgeUrl},n={type:"http",connectEvent:e.connectEvent,session:t,lastWalletEventId:e.lastWalletEventId,nextRpcRequestId:e.nextRpcRequestId};return this.storage.setItem(this.storeKey,JSON.stringify(n))}let t={type:"http",connectionSource:e.connectionSource,sessionCrypto:e.sessionCrypto.stringifyKeypair(),createdAt:Date.now()};return this.storage.setItem(this.storeKey,JSON.stringify(t))})}removeConnection(){return w(this,void 0,void 0,function*(){return this.storage.removeItem(this.storeKey)})}getConnection(){return w(this,void 0,void 0,function*(){var e;let t=yield this.storage.getItem(this.storeKey);if(!t)return null;let n=JSON.parse(t);if("injected"===n.type)return n;if("connectEvent"in n){let e=new f(n.session.sessionKeyPair);return{type:"http",connectEvent:n.connectEvent,lastWalletEventId:n.lastWalletEventId,nextRpcRequestId:n.nextRpcRequestId,session:{sessionCrypto:e,bridgeUrl:n.session.bridgeUrl,walletPublicKey:n.session.walletPublicKey}}}return Date.now()-(null!==(e=n.createdAt)&&void 0!==e?e:0)>3e5?(yield this.removeConnection(),null):{type:"http",sessionCrypto:new f(n.sessionCrypto),connectionSource:n.connectionSource}})}getHttpConnection(){return w(this,void 0,void 0,function*(){let e=yield this.getConnection();if(!e)throw new v("Trying to read HTTP connection source while nothing is stored");if("injected"===e.type)throw new v("Trying to read HTTP connection source while injected connection is stored");return e})}getHttpPendingConnection(){return w(this,void 0,void 0,function*(){let e=yield this.getConnection();if(!e)throw new v("Trying to read HTTP connection source while nothing is stored");if("injected"===e.type)throw new v("Trying to read HTTP connection source while injected connection is stored");if(!ee(e))throw new v("Trying to read HTTP-pending connection while http connection is stored");return e})}getInjectedConnection(){return w(this,void 0,void 0,function*(){let e=yield this.getConnection();if(!e)throw new v("Trying to read Injected bridge connection source while nothing is stored");if((null==e?void 0:e.type)==="http")throw new v("Trying to read Injected bridge connection source while HTTP connection is stored");return e})}storedConnectionType(){return w(this,void 0,void 0,function*(){let e=yield this.storage.getItem(this.storeKey);return e?JSON.parse(e).type:null})}storeLastWalletEventId(e){return w(this,void 0,void 0,function*(){let t=yield this.getConnection();if(t&&"http"===t.type&&!ee(t))return t.lastWalletEventId=e,this.storeConnection(t)})}getLastWalletEventId(){return w(this,void 0,void 0,function*(){let e=yield this.getConnection();if(e&&"lastWalletEventId"in e)return e.lastWalletEventId})}increaseNextRpcRequestId(){return w(this,void 0,void 0,function*(){let e=yield this.getConnection();if(e&&"nextRpcRequestId"in e){let t=e.nextRpcRequestId||0;return e.nextRpcRequestId=t+1,this.storeConnection(e)}})}getNextRpcRequestId(){return w(this,void 0,void 0,function*(){let e=yield this.getConnection();return e&&"nextRpcRequestId"in e&&e.nextRpcRequestId||0})}}class en{constructor(e,t){this.storage=e,this.walletConnectionSource=t,this.type="http",this.standardUniversalLink="tc://",this.pendingRequests=new Map,this.session=null,this.gateway=null,this.pendingGateways=[],this.listeners=[],this.defaultOpeningDeadlineMS=12e3,this.defaultRetryTimeoutMS=2e3,this.connectionStorage=new et(e)}static fromStorage(e){return w(this,void 0,void 0,function*(){let t=new et(e),n=yield t.getHttpConnection();return ee(n)?new en(e,n.connectionSource):new en(e,{bridgeUrl:n.session.bridgeUrl})})}connect(e,t){var n;let i=Y(null==t?void 0:t.signal);null===(n=this.abortController)||void 0===n||n.abort(),this.abortController=i,this.closeGateways();let r=new f;this.session={sessionCrypto:r,bridgeUrl:"bridgeUrl"in this.walletConnectionSource?this.walletConnectionSource.bridgeUrl:""},this.connectionStorage.storeConnection({type:"http",connectionSource:this.walletConnectionSource,sessionCrypto:r}).then(()=>w(this,void 0,void 0,function*(){i.signal.aborted||(yield J(e=>{var n;return this.openGateways(r,{openingDeadlineMS:null!==(n=null==t?void 0:t.openingDeadlineMS)&&void 0!==n?n:this.defaultOpeningDeadlineMS,signal:null==e?void 0:e.signal})},{attempts:Number.MAX_SAFE_INTEGER,delayMs:this.defaultRetryTimeoutMS,signal:i.signal}))}));let o="universalLink"in this.walletConnectionSource&&this.walletConnectionSource.universalLink?this.walletConnectionSource.universalLink:this.standardUniversalLink;return this.generateUniversalLink(o,e)}restoreConnection(e){var t,n;return w(this,void 0,void 0,function*(){let i=Y(null==e?void 0:e.signal);if(null===(t=this.abortController)||void 0===t||t.abort(),this.abortController=i,i.signal.aborted)return;this.closeGateways();let r=yield this.connectionStorage.getHttpConnection();if(!r||i.signal.aborted)return;let o=null!==(n=null==e?void 0:e.openingDeadlineMS)&&void 0!==n?n:this.defaultOpeningDeadlineMS;if(ee(r))return this.session={sessionCrypto:r.sessionCrypto,bridgeUrl:"bridgeUrl"in this.walletConnectionSource?this.walletConnectionSource.bridgeUrl:""},yield this.openGateways(r.sessionCrypto,{openingDeadlineMS:o,signal:null==i?void 0:i.signal});if(Array.isArray(this.walletConnectionSource))throw new v("Internal error. Connection source is array while WalletConnectionSourceHTTP was expected.");if(this.session=r.session,this.gateway&&(Q("Gateway is already opened, closing previous gateway"),yield this.gateway.close()),this.gateway=new Z(this.storage,this.walletConnectionSource.bridgeUrl,r.session.sessionCrypto.sessionId,this.gatewayListener.bind(this),this.gatewayErrorsListener.bind(this)),!i.signal.aborted){this.listeners.forEach(e=>e(r.connectEvent));try{yield J(e=>this.gateway.registerSession({openingDeadlineMS:o,signal:e.signal}),{attempts:Number.MAX_SAFE_INTEGER,delayMs:this.defaultRetryTimeoutMS,signal:i.signal})}catch(e){yield this.disconnect({signal:i.signal});return}}})}sendRequest(e,t){let n={};return"function"==typeof t?n.onRequestSent=t:(n.onRequestSent=null==t?void 0:t.onRequestSent,n.signal=null==t?void 0:t.signal,n.attempts=null==t?void 0:t.attempts),new Promise((t,i)=>w(this,void 0,void 0,function*(){var r;if(!this.gateway||!this.session||!("walletPublicKey"in this.session))throw new v("Trying to send bridge request without session");let o=(yield this.connectionStorage.getNextRpcRequestId()).toString();yield this.connectionStorage.increaseNextRpcRequestId(),Q("Send http-bridge request:",Object.assign(Object.assign({},e),{id:o}));let s=this.session.sessionCrypto.encrypt(JSON.stringify(Object.assign(Object.assign({},e),{id:o})),p(this.session.walletPublicKey));try{yield this.gateway.send(s,this.session.walletPublicKey,e.method,{attempts:null==n?void 0:n.attempts,signal:null==n?void 0:n.signal}),null===(r=null==n?void 0:n.onRequestSent)||void 0===r||r.call(n),this.pendingRequests.set(o.toString(),t)}catch(e){i(e)}}))}closeConnection(){this.closeGateways(),this.listeners=[],this.session=null,this.gateway=null}disconnect(e){return w(this,void 0,void 0,function*(){return new Promise(t=>w(this,void 0,void 0,function*(){let n=!1,i=null,r=()=>{n||(n=!0,this.removeBridgeAndSession().then(t))};try{this.closeGateways();let t=Y(null==e?void 0:e.signal);i=setTimeout(()=>{t.abort()},this.defaultOpeningDeadlineMS),yield this.sendRequest({method:"disconnect",params:[]},{onRequestSent:r,signal:t.signal,attempts:1})}catch(e){Q("Disconnect error:",e),n||this.removeBridgeAndSession().then(t)}finally{i&&clearTimeout(i),r()}}))})}listen(e){return this.listeners.push(e),()=>this.listeners=this.listeners.filter(t=>t!==e)}pause(){var e;null===(e=this.gateway)||void 0===e||e.pause(),this.pendingGateways.forEach(e=>e.pause())}unPause(){return w(this,void 0,void 0,function*(){let e=this.pendingGateways.map(e=>e.unPause());this.gateway&&e.push(this.gateway.unPause()),yield Promise.all(e)})}pendingGatewaysListener(e,t,n){return w(this,void 0,void 0,function*(){if(!this.pendingGateways.includes(e)){yield e.close();return}return this.closeGateways({except:e}),this.gateway&&(Q("Gateway is already opened, closing previous gateway"),yield this.gateway.close()),this.session.bridgeUrl=t,this.gateway=e,this.gateway.setErrorsListener(this.gatewayErrorsListener.bind(this)),this.gateway.setListener(this.gatewayListener.bind(this)),this.gatewayListener(n)})}gatewayListener(e){return w(this,void 0,void 0,function*(){let t=JSON.parse(this.session.sessionCrypto.decrypt(u.decode(e.message).toUint8Array(),p(e.from)));if(Q("Wallet message received:",t),!("event"in t)){let e=t.id.toString(),n=this.pendingRequests.get(e);if(!n){Q(`Response id ${e} doesn't match any request's id`);return}n(t),this.pendingRequests.delete(e);return}if(void 0!==t.id){let e=yield this.connectionStorage.getLastWalletEventId();if(void 0!==e&&t.id<=e){X(`Received event id (=${t.id}) must be greater than stored last wallet event id (=${e}) `);return}"connect"!==t.event&&(yield this.connectionStorage.storeLastWalletEventId(t.id))}let n=this.listeners;"connect"===t.event&&(yield this.updateSession(t,e.from)),"disconnect"===t.event&&(Q("Removing bridge and session: received disconnect event"),yield this.removeBridgeAndSession()),n.forEach(e=>e(t))})}gatewayErrorsListener(e){return w(this,void 0,void 0,function*(){throw new v(`Bridge error ${JSON.stringify(e)}`)})}updateSession(e,t){return w(this,void 0,void 0,function*(){this.session=Object.assign(Object.assign({},this.session),{walletPublicKey:t});let n=e.payload.items.find(e=>"ton_addr"===e.name),i=Object.assign(Object.assign({},e),{payload:Object.assign(Object.assign({},e.payload),{items:[n]})});yield this.connectionStorage.storeConnection({type:"http",session:this.session,lastWalletEventId:e.id,connectEvent:i,nextRpcRequestId:0})})}removeBridgeAndSession(){return w(this,void 0,void 0,function*(){this.closeConnection(),yield this.connectionStorage.removeConnection()})}generateUniversalLink(e,t){return z(e)?this.generateTGUniversalLink(e,t):this.generateRegularUniversalLink(e,t)}generateRegularUniversalLink(e,t){let n=new URL(e);return n.searchParams.append("v","2"),n.searchParams.append("id",this.session.sessionCrypto.sessionId),n.searchParams.append("r",JSON.stringify(t)),n.toString()}generateTGUniversalLink(e,t){let n="tonconnect-"+H(this.generateRegularUniversalLink("about:blank",t).split("?")[1]),i=new URL(this.convertToDirectLink(e));return i.searchParams.append("startapp",n),i.toString()}convertToDirectLink(e){let t=new URL(e);return t.searchParams.has("attach")&&(t.searchParams.delete("attach"),t.pathname+="/start"),t.toString()}openGateways(e,t){return w(this,void 0,void 0,function*(){if(!Array.isArray(this.walletConnectionSource))return this.gateway&&(Q("Gateway is already opened, closing previous gateway"),yield this.gateway.close()),this.gateway=new Z(this.storage,this.walletConnectionSource.bridgeUrl,e.sessionId,this.gatewayListener.bind(this),this.gatewayErrorsListener.bind(this)),yield this.gateway.registerSession({openingDeadlineMS:null==t?void 0:t.openingDeadlineMS,signal:null==t?void 0:t.signal});this.pendingGateways.map(e=>e.close().catch()),this.pendingGateways=this.walletConnectionSource.map(t=>{let n=new Z(this.storage,t.bridgeUrl,e.sessionId,()=>{},()=>{});return n.setListener(e=>this.pendingGatewaysListener(n,t.bridgeUrl,e)),n}),yield Promise.allSettled(this.pendingGateways.map(e=>J(n=>{var i;return this.pendingGateways.some(t=>t===e)?e.registerSession({openingDeadlineMS:null!==(i=null==t?void 0:t.openingDeadlineMS)&&void 0!==i?i:this.defaultOpeningDeadlineMS,signal:n.signal}):e.close()},{attempts:Number.MAX_SAFE_INTEGER,delayMs:this.defaultRetryTimeoutMS,signal:null==t?void 0:t.signal})))})}closeGateways(e){var t;null===(t=this.gateway)||void 0===t||t.close(),this.pendingGateways.filter(t=>t!==(null==e?void 0:e.except)).forEach(e=>e.close()),this.pendingGateways=[]}}function ei(e,t){return!!e&&"object"==typeof e&&t.every(t=>t in e)}class er{constructor(){this.storage={}}static getInstance(){return er.instance||(er.instance=new er),er.instance}get length(){return Object.keys(this.storage).length}clear(){this.storage={}}getItem(e){var t;return null!==(t=this.storage[e])&&void 0!==t?t:null}key(e){var t;let n=Object.keys(this.storage);return e<0||e>=n.length?null:null!==(t=n[e])&&void 0!==t?t:null}removeItem(e){delete this.storage[e]}setItem(e,t){this.storage[e]=t}}function eo(){if("undefined"!=typeof window)return window}class es{constructor(e,t){this.injectedWalletKey=t,this.type="injected",this.unsubscribeCallback=null,this.listenSubscriptions=!1,this.listeners=[];let n=es.window;if(!es.isWindowContainsWallet(n,t))throw new E;this.connectionStorage=new et(e),this.injectedWallet=n[t].tonconnect}static fromStorage(e){return w(this,void 0,void 0,function*(){let t=new et(e);return new es(e,(yield t.getInjectedConnection()).jsBridgeKey)})}static isWalletInjected(e){return es.isWindowContainsWallet(this.window,e)}static isInsideWalletBrowser(e){return!!es.isWindowContainsWallet(this.window,e)&&this.window[e].tonconnect.isWalletBrowser}static getCurrentlyInjectedWallets(){return this.window?(function(){let e=eo();if(!e)return[];try{return Object.keys(e)}catch(e){return[]}})().filter(([e,t])=>(function(e){try{var t,n,i;if(t="tonconnect",!ei(e,[t])||(n=e.tonconnect,i="walletInfo",!ei(n,[i])))return!1;return ei(e.tonconnect.walletInfo,["name","app_name","image","about_url","platforms"])}catch(e){return!1}})(t)).map(([e,t])=>({name:t.tonconnect.walletInfo.name,appName:t.tonconnect.walletInfo.app_name,aboutUrl:t.tonconnect.walletInfo.about_url,imageUrl:t.tonconnect.walletInfo.image,tondns:t.tonconnect.walletInfo.tondns,jsBridgeKey:e,injected:!0,embedded:t.tonconnect.isWalletBrowser,platforms:t.tonconnect.walletInfo.platforms,features:t.tonconnect.walletInfo.features})):[]}static isWindowContainsWallet(e,t){return!!e&&t in e&&"object"==typeof e[t]&&"tonconnect"in e[t]}connect(e){this._connect(2,e)}restoreConnection(){return w(this,void 0,void 0,function*(){try{Q("Injected Provider restoring connection...");let e=yield this.injectedWallet.restoreConnection();Q("Injected Provider restoring connection response",e),"connect"===e.event?(this.makeSubscriptions(),this.listeners.forEach(t=>t(e))):yield this.connectionStorage.removeConnection()}catch(e){yield this.connectionStorage.removeConnection(),console.error(e)}})}closeConnection(){this.listenSubscriptions&&this.injectedWallet.disconnect(),this.closeAllListeners()}disconnect(){return w(this,void 0,void 0,function*(){return new Promise(e=>{let t=()=>{this.closeAllListeners(),this.connectionStorage.removeConnection().then(e)};try{this.injectedWallet.disconnect(),t()}catch(e){Q(e),this.sendRequest({method:"disconnect",params:[]},t)}})})}closeAllListeners(){var e;this.listenSubscriptions=!1,this.listeners=[],null===(e=this.unsubscribeCallback)||void 0===e||e.call(this)}listen(e){return this.listeners.push(e),()=>this.listeners=this.listeners.filter(t=>t!==e)}sendRequest(e,t){var n;return w(this,void 0,void 0,function*(){let i={};"function"==typeof t?i.onRequestSent=t:(i.onRequestSent=null==t?void 0:t.onRequestSent,i.signal=null==t?void 0:t.signal);let r=(yield this.connectionStorage.getNextRpcRequestId()).toString();yield this.connectionStorage.increaseNextRpcRequestId(),Q("Send injected-bridge request:",Object.assign(Object.assign({},e),{id:r}));let o=this.injectedWallet.send(Object.assign(Object.assign({},e),{id:r}));return o.then(e=>Q("Wallet message received:",e)),null===(n=null==i?void 0:i.onRequestSent)||void 0===n||n.call(i),o})}_connect(e,t){return w(this,void 0,void 0,function*(){try{Q(`Injected Provider connect request: protocolVersion: ${e}, message:`,t);let n=yield this.injectedWallet.connect(e,t);Q("Injected Provider connect response:",n),"connect"===n.event&&(yield this.updateSession(),this.makeSubscriptions()),this.listeners.forEach(e=>e(n))}catch(t){Q("Injected Provider connect error:",t);let e={event:"connect_error",payload:{code:0,message:null==t?void 0:t.toString()}};this.listeners.forEach(t=>t(e))}})}makeSubscriptions(){this.listenSubscriptions=!0,this.unsubscribeCallback=this.injectedWallet.listen(e=>{Q("Wallet message received:",e),this.listenSubscriptions&&this.listeners.forEach(t=>t(e)),"disconnect"===e.event&&this.disconnect()})}updateSession(){return this.connectionStorage.storeConnection({type:"injected",jsBridgeKey:this.injectedWalletKey,nextRpcRequestId:0})}}es.window=eo();class ea{constructor(){this.localStorage=function(){if(function(){try{return"undefined"!=typeof localStorage}catch(e){return!1}}())return localStorage;if(void 0!==b&&null!=b.versions&&null!=b.versions.node)throw new v("`localStorage` is unavailable, but it is required for TonConnect. For more details, see https://github.com/ton-connect/sdk/tree/main/packages/sdk#init-connector");return er.getInstance()}()}getItem(e){return w(this,void 0,void 0,function*(){return this.localStorage.getItem(e)})}removeItem(e){return w(this,void 0,void 0,function*(){this.localStorage.removeItem(e)})}setItem(e,t){return w(this,void 0,void 0,function*(){this.localStorage.setItem(e,t)})}}function el(e){return"jsBridgeKey"in e&&e.injected}function ec(e){return el(e)&&e.embedded}function ed(e){return"bridgeUrl"in e}let eu=[{app_name:"telegram-wallet",name:"Wallet",image:"https://wallet.tg/images/logo-288.png",about_url:"https://wallet.tg/",universal_url:"https://t.me/wallet?attach=wallet",bridge:[{type:"sse",url:"https://walletbot.me/tonconnect-bridge/bridge"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"tonkeeper",name:"Tonkeeper",image:"https://tonkeeper.com/assets/tonconnect-icon.png",tondns:"tonkeeper.ton",about_url:"https://tonkeeper.com",universal_url:"https://app.tonkeeper.com/ton-connect",deepLink:"tonkeeper-tc://",bridge:[{type:"sse",url:"https://bridge.tonapi.io/bridge"},{type:"js",key:"tonkeeper"}],platforms:["ios","android","chrome","firefox","macos"]},{app_name:"mytonwallet",name:"MyTonWallet",image:"https://static.mytonwallet.io/icon-256.png",about_url:"https://mytonwallet.io",universal_url:"https://connect.mytonwallet.org",bridge:[{type:"js",key:"mytonwallet"},{type:"sse",url:"https://tonconnectbridge.mytonwallet.org/bridge/"}],platforms:["chrome","windows","macos","linux","ios","android","firefox"]},{app_name:"tonhub",name:"Tonhub",image:"https://tonhub.com/tonconnect_logo.png",about_url:"https://tonhub.com",universal_url:"https://tonhub.com/ton-connect",bridge:[{type:"js",key:"tonhub"},{type:"sse",url:"https://connect.tonhubapi.com/tonconnect"}],platforms:["ios","android"]},{app_name:"bitgetTonWallet",name:"Bitget Wallet",image:"https://raw.githubusercontent.com/bitgetwallet/download/refs/heads/main/logo/png/bitget_wallet_logo_288_mini.png",about_url:"https://web3.bitget.com",deepLink:"bitkeep://",bridge:[{type:"js",key:"bitgetTonWallet"},{type:"sse",url:"https://ton-connect-bridge.bgwapi.io/bridge"}],platforms:["ios","android","chrome"],universal_url:"https://bkcode.vip/ton-connect"},{app_name:"okxMiniWallet",name:"OKX Mini Wallet",image:"https://static.okx.com/cdn/assets/imgs/2411/8BE1A4A434D8F58A.png",about_url:"https://www.okx.com/web3",universal_url:"https://t.me/OKX_WALLET_BOT?attach=wallet",bridge:[{type:"sse",url:"https://www.okx.com/tonbridge/discover/rpc/bridge"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"binanceWeb3TonWallet",name:"Binance Web3 Wallet",image:"https://public.bnbstatic.com/static/binance-w3w/ton-provider/binancew3w.png",about_url:"https://www.binance.com/en/web3wallet",deepLink:"bnc://app.binance.com/cedefi/ton-connect",bridge:[{type:"js",key:"binancew3w"},{type:"sse",url:"https://wallet.binance.com/tonbridge/bridge"}],platforms:["ios","android","macos","windows","linux"],universal_url:"https://app.binance.com/cedefi/ton-connect"},{app_name:"fintopio-tg",name:"Fintopio",image:"https://fintopio.com/tonconnect-icon.png",about_url:"https://fintopio.com",universal_url:"https://t.me/fintopio?attach=wallet",bridge:[{type:"sse",url:"https://wallet-bridge.fintopio.com/bridge"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"okxTonWallet",name:"OKX Wallet",image:"https://static.okx.com/cdn/assets/imgs/247/58E63FEA47A2B7D7.png",about_url:"https://www.okx.com/web3",universal_url:"https://www.okx.com/download?appendQuery=true&deeplink=okx://web3/wallet/tonconnect",bridge:[{type:"js",key:"okxTonWallet"},{type:"sse",url:"https://www.okx.com/tonbridge/discover/rpc/bridge"}],platforms:["chrome","safari","firefox","ios","android"]},{app_name:"hot",name:"HOT",image:"https://raw.githubusercontent.com/hot-dao/media/main/logo.png",about_url:"https://hot-labs.org/",universal_url:"https://t.me/herewalletbot?attach=wallet",bridge:[{type:"sse",url:"https://sse-bridge.hot-labs.org"},{type:"js",key:"hotWallet"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"bybitTonWallet",name:"Bybit Wallet",image:"https://raw.githubusercontent.com/bybit-web3/bybit-web3.github.io/main/docs/images/bybit-logo.png",about_url:"https://www.bybit.com/web3",universal_url:"https://app.bybit.com/ton-connect",deepLink:"bybitapp://",bridge:[{type:"js",key:"bybitTonWallet"},{type:"sse",url:"https://api-node.bybit.com/spot/api/web3/bridge/ton/bridge"}],platforms:["ios","android","chrome"]},{app_name:"dewallet",name:"DeWallet",image:"https://raw.githubusercontent.com/delab-team/manifests-images/main/WalletAvatar.png",about_url:"https://delabwallet.com",universal_url:"https://t.me/dewallet?attach=wallet",bridge:[{type:"sse",url:"https://bridge.dewallet.pro/bridge"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"safepalwallet",name:"SafePal",image:"https://s.pvcliping.com/web/public_image/SafePal_x288.png",tondns:"",about_url:"https://www.safepal.com",universal_url:"https://link.safepal.io/ton-connect",deepLink:"safepal-tc://",bridge:[{type:"sse",url:"https://ton-bridge.safepal.com/tonbridge/v1/bridge"},{type:"js",key:"safepalwallet"}],platforms:["ios","android","chrome","firefox"]},{app_name:"GateWallet",name:"GateWallet",image:"https://img.gatedataimg.com/prd-ordinal-imgs/036f07bb8730716e/gateio-0925.png",about_url:"https://www.gate.io/",bridge:[{type:"js",key:"gatetonwallet"},{type:"sse",url:"https://dapp.gateio.services/tonbridge_api/bridge/v1"}],platforms:["ios","android"],universal_url:"https://gateio.go.link/gateio/web3?adj_t=1ff8khdw_1fu4ccc7"},{app_name:"openmask",name:"OpenMask",image:"https://raw.githubusercontent.com/OpenProduct/openmask-extension/main/public/openmask-logo-288.png",about_url:"https://www.openmask.app/",bridge:[{type:"js",key:"openmask"}],platforms:["chrome"]},{app_name:"BitgetWeb3",name:"BitgetWeb3",image:"https://img.bitgetimg.com/image/third/1731638059795.png",about_url:"​https://www.bitget.com",universal_url:"https://t.me/BitgetOfficialBot?attach=wallet",bridge:[{type:"sse",url:"https://ton-connect-bridge.bgwapi.io/bridge"}],platforms:["ios","android","windows","macos","linux"]},{app_name:"tobi",name:"Tobi",image:"https://app.tobiwallet.app/icons/logo-288.png",about_url:"https://tobi.fun",universal_url:"https://t.me/TobiCopilotBot?attach=wallet",bridge:[{type:"sse",url:"https://ton-bridge.tobiwallet.app/bridge"}],platforms:["ios","android","macos","windows","linux"]},{app_name:"xtonwallet",name:"XTONWallet",image:"https://xtonwallet.com/assets/img/icon-256-back.png",about_url:"https://xtonwallet.com",bridge:[{type:"js",key:"xtonwallet"}],platforms:["chrome","firefox"]},{app_name:"tonwallet",name:"TON Wallet",image:"https://wallet.ton.org/assets/ui/qr-logo.png",about_url:"https://chrome.google.com/webstore/detail/ton-wallet/nphplpgoakhhjchkkhmiggakijnkhfnd",bridge:[{type:"js",key:"tonwallet"}],platforms:["chrome"]}];class eh{constructor(e){var t;this.walletsListCache=null,this.walletsListCacheCreationTimestamp=null,this.walletsListSource=null!==(t=null==e?void 0:e.walletsListSource)&&void 0!==t?t:"https://raw.githubusercontent.com/ton-blockchain/wallets-list/main/wallets-v2.json",this.cacheTTLMs=null==e?void 0:e.cacheTTLMs}getWallets(){return w(this,void 0,void 0,function*(){return this.cacheTTLMs&&this.walletsListCacheCreationTimestamp&&Date.now()>this.walletsListCacheCreationTimestamp+this.cacheTTLMs&&(this.walletsListCache=null),this.walletsListCache||(this.walletsListCache=this.fetchWalletsList(),this.walletsListCache.then(()=>{this.walletsListCacheCreationTimestamp=Date.now()}).catch(()=>{this.walletsListCache=null,this.walletsListCacheCreationTimestamp=null})),this.walletsListCache})}getEmbeddedWallet(){return w(this,void 0,void 0,function*(){let e=(yield this.getWallets()).filter(ec);return 1===e.length?e[0]:null})}fetchWalletsList(){return w(this,void 0,void 0,function*(){let e=[];try{let t=yield fetch(this.walletsListSource);if(e=yield t.json(),!Array.isArray(e))throw new A("Wrong wallets list format, wallets list must be an array.");let n=e.filter(e=>!this.isCorrectWalletConfigDTO(e));n.length&&(X(`Wallet(s) ${n.map(e=>(null==e?void 0:e.name)||"unknown").join(", ")} config format is wrong. They were removed from the wallets list.`),e=e.filter(e=>this.isCorrectWalletConfigDTO(e)))}catch(t){X(t),e=eu}let t=[];try{t=es.getCurrentlyInjectedWallets()}catch(e){X(e)}return this.mergeWalletsLists(this.walletConfigDTOListToWalletConfigList(e),t)})}walletConfigDTOListToWalletConfigList(e){return e.map(e=>{let t={name:e.name,appName:e.app_name,imageUrl:e.image,aboutUrl:e.about_url,tondns:e.tondns,platforms:e.platforms,features:e.features};return e.bridge.forEach(n=>{if("sse"===n.type&&(t.bridgeUrl=n.url,t.universalLink=e.universal_url,t.deepLink=e.deepLink),"js"===n.type){let e=n.key;t.jsBridgeKey=e,t.injected=es.isWalletInjected(e),t.embedded=es.isInsideWalletBrowser(e)}}),t})}mergeWalletsLists(e,t){return[...new Set(e.concat(t).map(e=>e.name)).values()].map(n=>{let i=e.find(e=>e.name===n),r=t.find(e=>e.name===n);return Object.assign(Object.assign({},i&&Object.assign({},i)),r&&Object.assign({},r))})}isCorrectWalletConfigDTO(e){if(!e||"object"!=typeof e)return!1;let t="name"in e,n="app_name"in e,i="image"in e,r="about_url"in e,o="platforms"in e;if(!t||!i||!r||!o||!n||!e.platforms||!Array.isArray(e.platforms)||!e.platforms.length||!("bridge"in e)||!Array.isArray(e.bridge)||!e.bridge.length)return!1;let s=e.bridge;if(s.some(e=>!e||"object"!=typeof e||!("type"in e)))return!1;let a=s.find(e=>"sse"===e.type);if(a&&(!("object"==typeof a&&"url"in a)||!a.url||!e.universal_url))return!1;let l=s.find(e=>"js"===e.type);return!l||"object"==typeof l&&"key"in l&&!!l.key}}function ep(e,t){if("object"!=typeof t)return!0;let{sendTransaction:n,signData:i}=t;if(n){let t=ef(e,"SendTransaction");if(!t||!function(e,t){let n=void 0===t.minMessages||t.minMessages<=e.maxMessages,i=!t.extraCurrencyRequired||e.extraCurrencySupported;return!!(n&&i)}(t,n))return!1}if(i){let t=ef(e,"SignData");if(!t||!i.types.every(e=>t.types.includes(e)))return!1}return!0}function ef(e,t){return e.find(e=>e&&"object"==typeof e&&e.name===t)}function eb(){return{type:"request-version"}}function eg(e){return{type:"response-version",version:e}}function ew(e){return{ton_connect_sdk_lib:e.ton_connect_sdk_lib,ton_connect_ui_lib:e.ton_connect_ui_lib}}function ev(e,t){var n,i,r,o,s,a,l,c;let d=(null===(n=null==t?void 0:t.connectItems)||void 0===n?void 0:n.tonProof)&&"proof"in t.connectItems.tonProof;return{wallet_address:null!==(r=null===(i=null==t?void 0:t.account)||void 0===i?void 0:i.address)&&void 0!==r?r:null,wallet_type:null!==(o=null==t?void 0:t.device.appName)&&void 0!==o?o:null,wallet_version:null!==(s=null==t?void 0:t.device.appVersion)&&void 0!==s?s:null,auth_type:d?"ton_proof":"ton_addr",custom_data:Object.assign({chain_id:null!==(l=null===(a=null==t?void 0:t.account)||void 0===a?void 0:a.chain)&&void 0!==l?l:null,provider:null!==(c=null==t?void 0:t.provider)&&void 0!==c?c:null},ew(e))}}function ey(e){return{type:"connection-started",custom_data:ew(e)}}function em(e,t){return Object.assign({type:"connection-completed",is_success:!0},ev(e,t))}function ex(e,t,n){return{type:"connection-error",is_success:!1,error_message:t,error_code:null!=n?n:null,custom_data:ew(e)}}function e_(e){return{type:"connection-restoring-started",custom_data:ew(e)}}function eS(e,t){return Object.assign({type:"connection-restoring-completed",is_success:!0},ev(e,t))}function eE(e,t){return{type:"connection-restoring-error",is_success:!1,error_message:t,custom_data:ew(e)}}function eR(e,t){var n,i,r,o;return{valid_until:null!==(n=String(t.validUntil))&&void 0!==n?n:null,from:null!==(o=null!==(i=t.from)&&void 0!==i?i:null===(r=null==e?void 0:e.account)||void 0===r?void 0:r.address)&&void 0!==o?o:null,messages:t.messages.map(e=>{var t,n;return{address:null!==(t=e.address)&&void 0!==t?t:null,amount:null!==(n=e.amount)&&void 0!==n?n:null}})}}function ek(e,t,n){return Object.assign(Object.assign({type:"transaction-sent-for-signature"},ev(e,t)),eR(t,n))}function eO(e,t,n,i){return Object.assign(Object.assign({type:"transaction-signed",is_success:!0,signed_transaction:i.boc},ev(e,t)),eR(t,n))}function eC(e,t,n,i,r){return Object.assign(Object.assign({type:"transaction-signing-failed",is_success:!1,error_message:i,error_code:null!=r?r:null},ev(e,t)),eR(t,n))}function eT(e,t,n){return Object.assign({type:"sign-data-request-initiated",data:n},ev(e,t))}function eA(e,t,n,i){return Object.assign({type:"sign-data-request-completed",is_success:!0,data:n,signed_data:i},ev(e,t))}function eU(e,t,n,i,r){return Object.assign({type:"sign-data-request-failed",is_success:!1,data:n,error_message:i,error_code:null!=r?r:null},ev(e,t))}function eN(e,t,n){return Object.assign({type:"disconnection",scope:n},ev(e,t))}class eP{constructor(){this.window=eo()}dispatchEvent(e,t){var n;return w(this,void 0,void 0,function*(){let i=new CustomEvent(e,{detail:t});null===(n=this.window)||void 0===n||n.dispatchEvent(i)})}addEventListener(e,t,n){var i;return w(this,void 0,void 0,function*(){return null===(i=this.window)||void 0===i||i.addEventListener(e,t,n),()=>{var n;return null===(n=this.window)||void 0===n?void 0:n.removeEventListener(e,t)}})}}class ej{constructor(e){var t;this.eventPrefix="ton-connect-",this.tonConnectUiVersion=null,this.eventDispatcher=null!==(t=null==e?void 0:e.eventDispatcher)&&void 0!==t?t:new eP,this.tonConnectSdkVersion=e.tonConnectSdkVersion,this.init().catch()}get version(){return ew({ton_connect_sdk_lib:this.tonConnectSdkVersion,ton_connect_ui_lib:this.tonConnectUiVersion})}init(){return w(this,void 0,void 0,function*(){try{yield this.setRequestVersionHandler(),this.tonConnectUiVersion=yield this.requestTonConnectUiVersion()}catch(e){}})}setRequestVersionHandler(){return w(this,void 0,void 0,function*(){yield this.eventDispatcher.addEventListener("ton-connect-request-version",()=>w(this,void 0,void 0,function*(){yield this.eventDispatcher.dispatchEvent("ton-connect-response-version",eg(this.tonConnectSdkVersion))}))})}requestTonConnectUiVersion(){return w(this,void 0,void 0,function*(){return new Promise((e,t)=>w(this,void 0,void 0,function*(){try{yield this.eventDispatcher.addEventListener("ton-connect-ui-response-version",t=>{e(t.detail.version)},{once:!0}),yield this.eventDispatcher.dispatchEvent("ton-connect-ui-request-version",eb())}catch(e){t(e)}}))})}dispatchUserActionEvent(e){try{this.eventDispatcher.dispatchEvent(`${this.eventPrefix}${e.type}`,e).catch()}catch(e){}}trackConnectionStarted(...e){try{let t=ey(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackConnectionCompleted(...e){try{let t=em(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackConnectionError(...e){try{let t=ex(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackConnectionRestoringStarted(...e){try{let t=e_(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackConnectionRestoringCompleted(...e){try{let t=eS(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackConnectionRestoringError(...e){try{let t=eE(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackDisconnection(...e){try{let t=eN(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackTransactionSentForSignature(...e){try{let t=ek(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackTransactionSigned(...e){try{let t=eO(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackTransactionSigningFailed(...e){try{let t=eC(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackDataSentForSignature(...e){try{let t=eT(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackDataSigned(...e){try{let t=eA(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}trackDataSigningFailed(...e){try{let t=eU(this.version,...e);this.dispatchUserActionEvent(t)}catch(e){}}}class eL{constructor(e){if(this.walletsList=new eh,this._wallet=null,this.provider=null,this.statusChangeSubscriptions=[],this.statusChangeErrorSubscriptions=[],this.dappSettings={manifestUrl:(null==e?void 0:e.manifestUrl)||function(){var e;let t=null===(e=eo())||void 0===e?void 0:e.location.origin;return t?t+"/tonconnect-manifest.json":""}(),storage:(null==e?void 0:e.storage)||new ea},this.walletsRequiredFeatures=null==e?void 0:e.walletsRequiredFeatures,this.walletsList=new eh({walletsListSource:null==e?void 0:e.walletsListSource,cacheTTLMs:null==e?void 0:e.walletsListCacheTTLMs}),this.tracker=new ej({eventDispatcher:null==e?void 0:e.eventDispatcher,tonConnectSdkVersion:"3.2.0"}),!this.dappSettings.manifestUrl)throw new y("Dapp tonconnect-manifest.json must be specified if window.location.origin is undefined. See more https://github.com/ton-connect/docs/blob/main/requests-responses.md#app-manifest");this.bridgeConnectionStorage=new et(this.dappSettings.storage),(null==e?void 0:e.disableAutoPauseConnection)||this.addWindowFocusAndBlurSubscriptions()}static getWallets(){return this.walletsList.getWallets()}get connected(){return null!==this._wallet}get account(){var e;return(null===(e=this._wallet)||void 0===e?void 0:e.account)||null}get wallet(){return this._wallet}set wallet(e){this._wallet=e,this.statusChangeSubscriptions.forEach(e=>e(this._wallet))}getWallets(){return this.walletsList.getWallets()}onStatusChange(e,t){return this.statusChangeSubscriptions.push(e),t&&this.statusChangeErrorSubscriptions.push(t),()=>{this.statusChangeSubscriptions=this.statusChangeSubscriptions.filter(t=>t!==e),t&&(this.statusChangeErrorSubscriptions=this.statusChangeErrorSubscriptions.filter(e=>e!==t))}}connect(e,t){var n,i;let r={};if("object"==typeof t&&"tonProof"in t&&(r.request=t),"object"==typeof t&&("openingDeadlineMS"in t||"signal"in t||"request"in t)&&(r.request=null==t?void 0:t.request,r.openingDeadlineMS=null==t?void 0:t.openingDeadlineMS,r.signal=null==t?void 0:t.signal),this.connected)throw new _;let o=Y(null==r?void 0:r.signal);if(null===(n=this.abortController)||void 0===n||n.abort(),this.abortController=o,o.signal.aborted)throw new v("Connection was aborted");return null===(i=this.provider)||void 0===i||i.closeConnection(),this.provider=this.createProvider(e),o.signal.addEventListener("abort",()=>{var e;null===(e=this.provider)||void 0===e||e.closeConnection(),this.provider=null}),this.tracker.trackConnectionStarted(),this.provider.connect(this.createConnectRequest(null==r?void 0:r.request),{openingDeadlineMS:null==r?void 0:r.openingDeadlineMS,signal:o.signal})}restoreConnection(e){var t,n;return w(this,void 0,void 0,function*(){this.tracker.trackConnectionRestoringStarted();let i=Y(null==e?void 0:e.signal);if(null===(t=this.abortController)||void 0===t||t.abort(),this.abortController=i,i.signal.aborted){this.tracker.trackConnectionRestoringError("Connection restoring was aborted");return}let[r,o]=yield Promise.all([this.bridgeConnectionStorage.storedConnectionType(),this.walletsList.getEmbeddedWallet()]);if(i.signal.aborted){this.tracker.trackConnectionRestoringError("Connection restoring was aborted");return}let s=null;try{switch(r){case"http":s=yield en.fromStorage(this.dappSettings.storage);break;case"injected":s=yield es.fromStorage(this.dappSettings.storage);break;default:if(!o)return;s=this.createProvider(o)}}catch(e){this.tracker.trackConnectionRestoringError("Provider is not restored"),yield this.bridgeConnectionStorage.removeConnection(),null==s||s.closeConnection(),s=null;return}if(i.signal.aborted){null==s||s.closeConnection(),this.tracker.trackConnectionRestoringError("Connection restoring was aborted");return}if(!s){X("Provider is not restored"),this.tracker.trackConnectionRestoringError("Provider is not restored");return}null===(n=this.provider)||void 0===n||n.closeConnection(),this.provider=s,s.listen(this.walletEventsListener.bind(this));let a=()=>{this.tracker.trackConnectionRestoringError("Connection restoring was aborted"),null==s||s.closeConnection(),s=null};return i.signal.addEventListener("abort",a),Promise.race([J(t=>w(this,void 0,void 0,function*(){yield null==s?void 0:s.restoreConnection({openingDeadlineMS:null==e?void 0:e.openingDeadlineMS,signal:t.signal}),i.signal.removeEventListener("abort",a),this.connected?this.tracker.trackConnectionRestoringCompleted(this.wallet):this.tracker.trackConnectionRestoringError("Connection restoring failed")}),{attempts:Number.MAX_SAFE_INTEGER,delayMs:2e3,signal:null==e?void 0:e.signal}),new Promise(e=>setTimeout(()=>e(),12e3))])})}sendTransaction(e,t){return w(this,void 0,void 0,function*(){let n={};"function"==typeof t?n.onRequestSent=t:(n.onRequestSent=null==t?void 0:t.onRequestSent,n.signal=null==t?void 0:t.signal);let i=Y(null==n?void 0:n.signal);if(i.signal.aborted)throw new v("Transaction sending was aborted");this.checkConnection();let r=e.messages.length,o=e.messages.some(e=>e.extraCurrency&&Object.keys(e.extraCurrency).length>0);!function(e,t){let n=e.includes("SendTransaction"),i=ef(e,"SendTransaction"),r={minMessages:t.requiredMessagesNumber,extraCurrencyRequired:t.requireExtraCurrencies};if(!n&&!i)throw new R("Wallet doesn't support SendTransaction feature.",{cause:{requiredFeature:{featureName:"SendTransaction",value:r}}});if(t.requireExtraCurrencies&&(!i||!i.extraCurrencySupported))throw new R("Wallet is not able to handle such SendTransaction request. Extra currencies support is required.",{cause:{requiredFeature:{featureName:"SendTransaction",value:r}}});if(i&&void 0!==i.maxMessages){if(i.maxMessages<t.requiredMessagesNumber)throw new R(`Wallet is not able to handle such SendTransaction request. Max support messages number is ${i.maxMessages}, but ${t.requiredMessagesNumber} is required.`,{cause:{requiredFeature:{featureName:"SendTransaction",value:r}}});return}!function(...e){try{console.warn("[TON_CONNECT_SDK]",...e)}catch(e){}}("Connected wallet didn't provide information about max allowed messages in the SendTransaction request. Request may be rejected by the wallet.")}(this.wallet.device.features,{requiredMessagesNumber:r,requireExtraCurrencies:o}),this.tracker.trackTransactionSentForSignature(this.wallet,e);let{validUntil:s,messages:a}=e,l=g(e,["validUntil","messages"]),c=e.from||this.account.address,d=e.network||this.account.chain,u=yield this.provider.sendRequest(D.convertToRpcRequest(Object.assign(Object.assign({},l),{from:c,network:d,valid_until:s,messages:a.map(e=>{var{extraCurrency:t}=e;return Object.assign(Object.assign({},g(e,["extraCurrency"])),{extra_currency:t})})})),{onRequestSent:n.onRequestSent,signal:i.signal});if(D.isError(u))return this.tracker.trackTransactionSigningFailed(this.wallet,e,u.error.message,u.error.code),D.parseAndThrowError(u);let h=D.convertFromRpcResponse(u);return this.tracker.trackTransactionSigned(this.wallet,e,h),h})}signData(e,t){return w(this,void 0,void 0,function*(){let n=Y(null==t?void 0:t.signal);if(n.signal.aborted)throw new v("data sending was aborted");this.checkConnection(),function(e,t){let n=e.find(e=>e&&"object"==typeof e&&"SignData"===e.name);if(!n)throw new R("Wallet doesn't support SignData feature.",{cause:{requiredFeature:{featureName:"SignData",value:{types:t.requiredTypes}}}});let i=t.requiredTypes.filter(e=>!n.types.includes(e));if(i.length)throw new R(`Wallet doesn't support required SignData types: ${i.join(", ")}.`,{cause:{requiredFeature:{featureName:"SignData",value:{types:i}}}})}(this.wallet.device.features,{requiredTypes:[e.type]}),this.tracker.trackDataSentForSignature(this.wallet,e);let i=e.from||this.account.address,r=e.network||this.account.chain,o=yield this.provider.sendRequest(F.convertToRpcRequest(Object.assign(Object.assign({},e),{from:i,network:r})),{onRequestSent:null==t?void 0:t.onRequestSent,signal:n.signal});if(F.isError(o))return this.tracker.trackDataSigningFailed(this.wallet,e,o.error.message,o.error.code),F.parseAndThrowError(o);let s=F.convertFromRpcResponse(o);return this.tracker.trackDataSigned(this.wallet,e,s),s})}disconnect(e){var t;return w(this,void 0,void 0,function*(){if(!this.connected)throw new S;let n=Y(null==e?void 0:e.signal),i=this.abortController;if(this.abortController=n,n.signal.aborted)throw new v("Disconnect was aborted");this.onWalletDisconnected("dapp"),yield null===(t=this.provider)||void 0===t?void 0:t.disconnect({signal:n.signal}),null==i||i.abort()})}pauseConnection(){var e;(null===(e=this.provider)||void 0===e?void 0:e.type)==="http"&&this.provider.pause()}unPauseConnection(){var e;return(null===(e=this.provider)||void 0===e?void 0:e.type)!=="http"?Promise.resolve():this.provider.unPause()}addWindowFocusAndBlurSubscriptions(){let e=function(){if("undefined"!=typeof document)return document}();if(e)try{e.addEventListener("visibilitychange",()=>{e.hidden?this.pauseConnection():this.unPauseConnection().catch()})}catch(e){X("Cannot subscribe to the document.visibilitychange: ",e)}}createProvider(e){let t;return(t=!Array.isArray(e)&&"jsBridgeKey"in e?new es(this.dappSettings.storage,e.jsBridgeKey):new en(this.dappSettings.storage,e)).listen(this.walletEventsListener.bind(this)),t}walletEventsListener(e){switch(e.event){case"connect":this.onWalletConnected(e.payload);break;case"connect_error":this.tracker.trackConnectionError(e.payload.message,e.payload.code);let t=M.parseError(e.payload);this.onWalletConnectError(t);break;case"disconnect":this.onWalletDisconnected("wallet")}}onWalletConnected(e){var t;let n=e.items.find(e=>"ton_addr"===e.name),i=e.items.find(e=>"ton_proof"===e.name);if(!n)throw new v("ton_addr connection item was not found");if(!ep(e.device.features,this.walletsRequiredFeatures)){null===(t=this.provider)||void 0===t||t.disconnect(),this.onWalletConnectError(new k("Wallet does not support required features",{cause:{connectEvent:e}}));return}let o={device:e.device,provider:this.provider.type,account:{address:n.address,chain:n.network,walletStateInit:n.walletStateInit,publicKey:n.publicKey}};if(i){let e;try{if("proof"in i)e={name:"ton_proof",proof:{timestamp:i.proof.timestamp,domain:{lengthBytes:i.proof.domain.lengthBytes,value:i.proof.domain.value},payload:i.proof.payload,signature:i.proof.signature}};else if("error"in i)e={name:"ton_proof",error:{code:i.error.code,message:i.error.message}};else throw new v("Invalid data format")}catch(t){e={name:"ton_proof",error:{code:r.UNKNOWN_ERROR,message:"Invalid data format"}}}o.connectItems={tonProof:e}}this.wallet=o,this.tracker.trackConnectionCompleted(o)}onWalletConnectError(e){if(this.statusChangeErrorSubscriptions.forEach(t=>t(e)),Q(e),e instanceof x||e instanceof m)throw X(e),e}onWalletDisconnected(e){this.tracker.trackDisconnection(this.wallet,e),this.wallet=null}checkConnection(){if(!this.connected)throw new S}createConnectRequest(e){let t=[{name:"ton_addr"}];return(null==e?void 0:e.tonProof)&&t.push({name:"ton_proof",payload:e.tonProof}),{manifestUrl:this.dappSettings.manifestUrl,items:t}}}function eM(e,t=!1){let{wc:n,hex:i}=function(e){if(!e.includes(":"))throw new U(`Wrong address ${e}. Address must include ":".`);let t=e.split(":");if(2!==t.length)throw new U(`Wrong address ${e}. Address must include ":" only once.`);let n=parseInt(t[0]);if(0!==n&&-1!==n)throw new U(`Wrong address ${e}. WC must be eq 0 or -1, but ${n} received.`);let i=t[1];if((null==i?void 0:i.length)!==64)throw new U(`Wrong address ${e}. Hex part must be 64bytes length, but ${null==i?void 0:i.length} received.`);return{wc:n,hex:function(e){let t=(e=e.toLowerCase()).length;if(t%2!=0)throw new N("Hex string must have length a multiple of 2: "+e);let n=t/2,i=new Uint8Array(n);for(let t=0;t<n;t++){let n=2*t,r=e.substring(n,n+2);if(!eW.hasOwnProperty(r))throw new N("Invalid hex character: "+r);i[t]=eW[r]}return i}(i)}}(e),r=81;t&&(r|=128);let o=new Int8Array(34);o[0]=r,o[1]=n,o.set(i,2);let s=new Uint8Array(36);return s.set(o),s.set(function(e){let t=0,n=new Uint8Array(e.length+2);for(let i of(n.set(e),n)){let e=128;for(;e>0;)t<<=1,i&e&&(t+=1),e>>=1,t>65535&&(t&=65535,t^=4129)}return new Uint8Array([Math.floor(t/256),t%256])}(o),34),u.encode(s).replace(/\+/g,"-").replace(/\//g,"_")}eL.walletsList=new eh,eL.isWalletInjected=e=>es.isWalletInjected(e),eL.isInsideWalletBrowser=e=>es.isInsideWalletBrowser(e);let eW={};for(let e=0;e<=255;e++){let t=e.toString(16);t.length<2&&(t="0"+t),eW[t]=e}},9474:(e,t,n)=>{"use strict";n.d(t,{MJ:()=>m,c5:()=>x});var i=n(5155),r=n(2115),o=n(7664),s=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,d=(e,t,n)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,u=(e,t)=>{for(var n in t||(t={}))l.call(t,n)&&d(e,n,t[n]);if(a)for(var n of a(t))c.call(t,n)&&d(e,n,t[n]);return e},h=(e,t)=>{var n={};for(var i in e)l.call(e,i)&&0>t.indexOf(i)&&(n[i]=e[i]);if(null!=e&&a)for(var i of a(e))0>t.indexOf(i)&&c.call(e,i)&&(n[i]=e[i]);return n};function p(){return"undefined"!=typeof window}let f=(0,r.createContext)(null),b=null;(0,r.memo)(e=>{var{children:t}=e,n=h(e,["children"]);return p()&&!b&&(b=new o.i$(n)),(0,i.jsx)(f.Provider,{value:b,children:t})});class g extends o.WG{constructor(...e){super(...e),Object.setPrototypeOf(this,g.prototype)}}class w extends g{constructor(...e){super(...e),Object.setPrototypeOf(this,w.prototype)}}function v(){let e=(0,r.useContext)(f),t=(0,r.useCallback)(t=>{e&&(e.uiOptions=t)},[e]);return p()?(!function(e){if(!e)throw new w("You should add <TonConnectUIProvider> on the top of the app to use TonConnect")}(e),[e,t]):[null,()=>{}]}let y="ton-connect-button",m=(0,r.memo)(({className:e,style:t})=>{let[n,o]=v();return(0,r.useEffect)(()=>(o({buttonRootId:y}),()=>o({buttonRootId:null})),[o]),(0,i.jsx)("div",{id:y,className:e,style:u({width:"fit-content"},t)})});function x(){let[e]=v(),[t,n]=(0,r.useState)((null==e?void 0:e.wallet)||null);return(0,r.useEffect)(()=>{if(e)return n(e.wallet),e.onStatusChange(e=>{n(e)})},[e]),t}}}]);