import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { Cell, toNano, beginCell, Address } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import '@ton/test-utils';
import { sign } from '@ton/crypto';

describe('OnionAuction USDT Signature Verification', () => {

    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let buyer: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let signingKeyPair: { publicKey: Buffer; secretKey: Buffer };

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        buyer = await blockchain.treasury('buyer');

        // Generate proper signing key pair for testing  
        const crypto = await import('crypto');
        const seed = crypto.randomBytes(32);
        const { keyPairFromSeed } = await import('@ton/crypto');
        signingKeyPair = keyPairFromSeed(seed);

        // Deploy OnionAuction
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 3600n; // 1 hour

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                toNano('1000'),
                toNano('10000'),
                toNano('1000000')
            )
        );

        const deployResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            'Deploy'
        );
        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });

        // Start auction
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('1000'),
                hard_cap: toNano('10000'),
                initial_price: toNano('0.1')
            }
        );

        // Set signing key
        const publicKeyInt = BigInt('0x' + signingKeyPair.publicKey.toString('hex'));
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetSigningKey',
                public_key: publicKeyInt
            }
        );

        // Set USDT configuration (mock address for testing)
        const mockUsdtAddress = Address.parse('EQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM9c');
        const mockWalletAddress = Address.parse('EQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM9c');
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: mockUsdtAddress,
                usdt_wallet: mockWalletAddress
            }
        );
    });

    it('should handle signature verification for purchase calculation', async () => {
        const amount = 50000000000n; // 50000 USDT (above minimum)
        const currentPrice = toNano('0.1');
        const currentRound = 1;
        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = 12345n;

        // Calculate tokens to receive (same as contract logic)
        const usdtInTonUnits = amount * 1000n; // Convert 6 decimals to 9 decimals
        const tokensToReceive = (usdtInTonUnits * toNano('1')) / currentPrice;

        // Create purchase calculation
        const purchaseCalculation = {
            user: buyer.address,
            amount,
            currency: 1, // USDT
            tokensToReceive,
            currentPrice,
            currentRound,
            timestamp,
            nonce
        };

        // Create data cell for signing (same as contract hashPurchaseCalculation)
        const dataCell = beginCell()
            .storeAddress(purchaseCalculation.user)
            .storeCoins(purchaseCalculation.amount)
            .storeUint(purchaseCalculation.currency, 8)
            .storeCoins(purchaseCalculation.tokensToReceive)
            .storeCoins(purchaseCalculation.currentPrice)
            .storeUint(purchaseCalculation.currentRound, 32)
            .storeUint(purchaseCalculation.timestamp, 64)
            .storeUint(Number(purchaseCalculation.nonce), 64)
            .endCell();

        // Sign the data
        const dataHash = dataCell.hash();
        const signature = sign(dataHash, signingKeyPair.secretKey);

        // Create calculation object
        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: purchaseCalculation.user,
            amount: purchaseCalculation.amount,
            currency: BigInt(purchaseCalculation.currency),
            tokens_to_receive: purchaseCalculation.tokensToReceive,
            current_price: purchaseCalculation.currentPrice,
            current_round: BigInt(purchaseCalculation.currentRound),
            timestamp: BigInt(purchaseCalculation.timestamp),
            nonce: purchaseCalculation.nonce
        };

        // Convert signature to Slice
        const signatureSlice = beginCell().storeBuffer(signature).endCell().asSlice();

        // Test signature verification by calling contract with signature
        const purchaseResult = await onionAuction.send(
            buyer.getSender(),
            { value: toNano('0.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation,
                signature: signatureSlice
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: buyer.address,
            to: onionAuction.address,
            success: true,
        });
    });

    it('should reject purchase with invalid signature', async () => {
        const amount = 50000000000n; // 50000 USDT (above minimum)
        const currentPrice = toNano('0.1');
        const currentRound = 1;
        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = 12346n;

        const usdtInTonUnits = amount * 1000n;
        const tokensToReceive = (usdtInTonUnits * toNano('1')) / currentPrice;

        // Create purchase calculation
        const purchaseCalculation = {
            user: buyer.address,
            amount,
            currency: 1,
            tokensToReceive,
            currentPrice,
            currentRound,
            timestamp,
            nonce
        };

        // Create invalid signature (wrong data)
        const wrongDataCell = beginCell()
            .storeAddress(purchaseCalculation.user)
            .storeCoins(purchaseCalculation.amount + 1n) // Wrong amount
            .storeUint(purchaseCalculation.currency, 8)
            .storeCoins(purchaseCalculation.tokensToReceive)
            .storeCoins(purchaseCalculation.currentPrice)
            .storeUint(purchaseCalculation.currentRound, 32)
            .storeUint(purchaseCalculation.timestamp, 64)
            .storeUint(Number(purchaseCalculation.nonce), 64)
            .endCell();

        const wrongDataHash = wrongDataCell.hash();
        const invalidSignature = sign(wrongDataHash, signingKeyPair.secretKey);

        // Create calculation object
        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: purchaseCalculation.user,
            amount: purchaseCalculation.amount,
            currency: BigInt(purchaseCalculation.currency),
            tokens_to_receive: purchaseCalculation.tokensToReceive,
            current_price: purchaseCalculation.currentPrice,
            current_round: BigInt(purchaseCalculation.currentRound),
            timestamp: BigInt(purchaseCalculation.timestamp),
            nonce: purchaseCalculation.nonce
        };

        // Convert signature to Slice
        const invalidSignatureSlice = beginCell().storeBuffer(invalidSignature).endCell().asSlice();

        // Test with invalid signature
        const purchaseResult = await onionAuction.send(
            buyer.getSender(),
            { value: toNano('0.2') },
            {
                $$type: 'PurchaseWithSignature',
                calculation,
                signature: invalidSignatureSlice
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: buyer.address,
            to: onionAuction.address,
            success: false,
        });
    });

    it('should handle direct purchase without signature', async () => {
        const amount = toNano('100'); // 100 TON (above minimum)

        // Send direct purchase without signature
        const purchaseResult = await onionAuction.send(
            buyer.getSender(),
            { value: amount + toNano('0.2') }, // purchase amount + gas
            {
                $$type: 'Purchase',
                amount,
                currency: 0n // TON
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: buyer.address,
            to: onionAuction.address,
            success: true,
        });
    });

    it('should reject replay attacks with used nonce', async () => {
        const amount = 50000000000n; // 50000 USDT (above minimum)
        const currentPrice = toNano('0.1');
        const currentRound = 1;
        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = 12347n;

        const usdtInTonUnits = amount * 1000n;
        const tokensToReceive = (usdtInTonUnits * toNano('1')) / currentPrice;

        const purchaseCalculation = {
            user: buyer.address,
            amount,
            currency: 1,
            tokensToReceive,
            currentPrice,
            currentRound,
            timestamp,
            nonce
        };

        const dataCell = beginCell()
            .storeAddress(purchaseCalculation.user)
            .storeCoins(purchaseCalculation.amount)
            .storeUint(purchaseCalculation.currency, 8)
            .storeCoins(purchaseCalculation.tokensToReceive)
            .storeCoins(purchaseCalculation.currentPrice)
            .storeUint(purchaseCalculation.currentRound, 32)
            .storeUint(purchaseCalculation.timestamp, 64)
            .storeUint(Number(purchaseCalculation.nonce), 64)
            .endCell();

        const dataHash = dataCell.hash();
        const signature = sign(dataHash, signingKeyPair.secretKey);

        // Create calculation object
        const calculation = {
            $$type: 'PurchaseCalculation' as const,
            user: purchaseCalculation.user,
            amount: purchaseCalculation.amount,
            currency: BigInt(purchaseCalculation.currency),
            tokens_to_receive: purchaseCalculation.tokensToReceive,
            current_price: purchaseCalculation.currentPrice,
            current_round: BigInt(purchaseCalculation.currentRound),
            timestamp: BigInt(purchaseCalculation.timestamp),
            nonce: purchaseCalculation.nonce
        };

        // Convert signature to Slice
        const signatureSlice = beginCell().storeBuffer(signature).endCell().asSlice();

        const purchaseMessage = {
            $$type: 'PurchaseWithSignature' as const,
            calculation,
            signature: signatureSlice
        };

        // First purchase should succeed
        const firstPurchase = await onionAuction.send(
            buyer.getSender(),
            { value: toNano('0.2') },
            purchaseMessage
        );

        expect(firstPurchase.transactions).toHaveTransaction({
            from: buyer.address,
            to: onionAuction.address,
            success: true,
        });

        // Second purchase with same nonce should fail
        const secondPurchase = await onionAuction.send(
            buyer.getSender(),
            { value: toNano('0.2') },
            purchaseMessage
        );

        expect(secondPurchase.transactions).toHaveTransaction({
            from: buyer.address,
            to: onionAuction.address,
            success: false,
        });
    });
});
