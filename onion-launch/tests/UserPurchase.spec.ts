import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { toNano } from '@ton/core';
import { UserPurchase } from '../build/OnionAuction/OnionAuction_UserPurchase';
import '@ton/test-utils';

describe('UserPurchase', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let user: SandboxContract<TreasuryContract>;
    let auctionContract: SandboxContract<TreasuryContract>;
    let userPurchase: SandboxContract<UserPurchase>;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        user = await blockchain.treasury('user');
        auctionContract = await blockchain.treasury('auction');

        userPurchase = blockchain.openContract(
            await UserPurchase.fromInit(auctionContract.address, user.address)
        );

        const deployResult = await userPurchase.send(
            deployer.getSender(),
            { value: toNano('0.05') },
            'Deploy'
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: userPurchase.address,
            deploy: true,
            success: true,
        });
    });

    it('should deploy', async () => {
        // the check is done inside beforeEach
        // blockchain and userPurchase are ready to use
    });

    it('should create purchase record with direct method', async () => {
        const createPurchaseResult = await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('100'),
                tokens: toNano('1000'),
                currency: 0n, // TON
                purchase_method: 0n, // Direct
                nonce: 0n
            }
        );

        expect(createPurchaseResult.transactions).toHaveTransaction({
            from: auctionContract.address,
            to: userPurchase.address,
            success: true,
        });

        // Check purchase details
        const purchaseDetails = await userPurchase.getPurchaseDetails(1n);
        expect(purchaseDetails).toBeTruthy();
        expect(purchaseDetails!.purchase_method.toString()).toBe('0'); // Direct method
        expect(purchaseDetails!.nonce.toString()).toBe('0');
    });

    it('should create purchase record with signature verification method', async () => {
        const createPurchaseResult = await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('100'),
                tokens: toNano('1000'),
                currency: 0n, // TON
                purchase_method: 1n, // Signature verified
                nonce: 12345n
            }
        );

        expect(createPurchaseResult.transactions).toHaveTransaction({
            from: auctionContract.address,
            to: userPurchase.address,
            success: true,
        });

        // Check purchase details
        const purchaseDetails = await userPurchase.getPurchaseDetails(1n);
        expect(purchaseDetails).toBeTruthy();
        expect(purchaseDetails!.purchase_method.toString()).toBe('1'); // Signature verified method
        expect(purchaseDetails!.nonce.toString()).toBe('12345');
    });

    it('should track signature verified purchases count', async () => {
        // Create a direct purchase
        await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('50'),
                tokens: toNano('500'),
                currency: 0n,
                purchase_method: 0n, // Direct
                nonce: 0n
            }
        );

        // Create a signature verified purchase
        await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('100'),
                tokens: toNano('1000'),
                currency: 0n,
                purchase_method: 1n, // Signature verified
                nonce: 12345n
            }
        );

        // Create another signature verified purchase
        await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('200'),
                tokens: toNano('2000'),
                currency: 1n, // USDT
                purchase_method: 1n, // Signature verified
                nonce: 67890n
            }
        );

        // Check signature verified purchases count
        const signatureVerifiedCount = await userPurchase.getSignatureVerifiedPurchases();
        expect(signatureVerifiedCount.toString()).toBe('2');

        // Check total purchase count
        const totalCount = await userPurchase.getPurchaseIdCounter();
        expect(totalCount.toString()).toBe('3');
    });

    it('should handle refund for signature verified purchase', async () => {
        // Create a signature verified purchase
        await userPurchase.send(
            auctionContract.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('100'),
                tokens: toNano('1000'),
                currency: 0n,
                purchase_method: 1n, // Signature verified
                nonce: 12345n
            }
        );

        // Request refund
        const refundResult = await userPurchase.send(
            user.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'Refund',
                purchase_id: 1n
            }
        );

        expect(refundResult.transactions).toHaveTransaction({
            from: user.address,
            to: userPurchase.address,
            success: true,
        });

        // Check if refund was processed
        const isRefunded = await userPurchase.getIsRefunded(1n);
        expect(isRefunded).toBe(true);
    });

    it('should reject unauthorized purchase creation', async () => {
        const unauthorizedResult = await userPurchase.send(
            user.getSender(), // User trying to create purchase directly
            { value: toNano('0.1') },
            {
                $$type: 'CreateUserPurchase',
                user: user.address,
                amount: toNano('100'),
                tokens: toNano('1000'),
                currency: 0n,
                purchase_method: 1n,
                nonce: 12345n
            }
        );

        expect(unauthorizedResult.transactions).toHaveTransaction({
            from: user.address,
            to: userPurchase.address,
            success: false,
        });
    });
});
