import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { toNano } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction';
import '@ton/test-utils';

describe('OnionAuction USDT Integration', () => {
    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let buyer: SandboxContract<TreasuryContract>;
    let usdtMaster: SandboxContract<TreasuryContract>;
    let usdtWallet: SandboxContract<TreasuryContract>;

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        buyer = await blockchain.treasury('buyer');
        usdtMaster = await blockchain.treasury('usdt_master');
        usdtWallet = await blockchain.treasury('usdt_wallet');

        // Create auction with proper parameters
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n; // 24 hours later
        const softCap = toNano('500000'); // 500k TON
        const hardCap = toNano('2000000'); // 2M TON  
        const totalSupply = toNano('1000000'); // 1M tokens

        onionAuction = blockchain.openContract(
            await OnionAuction.fromInit(
                deployer.address,
                startTime,
                endTime,
                softCap,
                hardCap,
                totalSupply
            )
        );

        const deployResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.5') },
            'Deploy'
        );

        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });
    });

    it('should set USDT configuration', async () => {
        const setUSDTResult = await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: usdtMaster.address,
                usdt_wallet: usdtWallet.address
            }
        );

        expect(setUSDTResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            success: true,
        });

        // Check if USDT is enabled
        const isUSDTEnabled = await onionAuction.getIsUsdtEnabled();
        expect(isUSDTEnabled).toBe(true);

        // Check USDT config
        const usdtConfig = await onionAuction.getUsdtConfig();
        expect(usdtConfig?.master_address.toString()).toBe(usdtMaster.address.toString());
        expect(usdtConfig?.wallet_address?.toString()).toBe(usdtWallet.address.toString());
        expect(usdtConfig?.decimals.toString()).toBe('6');
    });

    it('should handle USDT purchase via JettonTransferNotification', async () => {
        // First set USDT configuration
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: usdtMaster.address,
                usdt_wallet: usdtWallet.address
            }
        );

        // Start the auction
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n;
        
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('500000'),
                hard_cap: toNano('2000000'),
                initial_price: toNano('0.1')
            }
        );

        // Simulate USDT transfer notification (50 USDT = 50,000,000 units with 6 decimals)
        const usdtAmount = 50000000n; // 50 USDT
        
        const purchaseResult = await onionAuction.send(
            usdtWallet.getSender(), // Message comes from USDT wallet
            { value: toNano('0.2') },
            {
                $$type: 'JettonTransferNotification',
                query_id: 0n,
                amount: usdtAmount,
                sender: buyer.address,
                forward_payload: null
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: usdtWallet.address,
            to: onionAuction.address,
            success: true,
        });

        // Check that USDT was recorded
        const totalRaisedUSDT = await onionAuction.getTotalRaisedUsdt();
        expect(totalRaisedUSDT.toString()).toBe(usdtAmount.toString());

        // Check total raised equivalent (USDT * 1000 to convert to TON units)
        const totalRaisedEquivalent = await onionAuction.getTotalRaisedEquivalent();
        expect(totalRaisedEquivalent.toString()).toBe((usdtAmount * 1000n).toString());

        // Check that tokens were sold
        const totalTokensSold = await onionAuction.getTotalTokensSold();
        expect(totalTokensSold > 0n).toBe(true);
    });

    it('should reject USDT transfer from wrong wallet', async () => {
        // Set USDT configuration
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: usdtMaster.address,
                usdt_wallet: usdtWallet.address
            }
        );

        // Start auction
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n;
        
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('500000'),
                hard_cap: toNano('2000000'),
                initial_price: toNano('0.1')
            }
        );

        // Try to send from wrong wallet (should fail)
        const fakeWallet = await blockchain.treasury('fake_wallet');
        
        const purchaseResult = await onionAuction.send(
            fakeWallet.getSender(), // Wrong wallet
            { value: toNano('0.2') },
            {
                $$type: 'JettonTransferNotification',
                query_id: 0n,
                amount: 50000000n,
                sender: buyer.address,
                forward_payload: null
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: fakeWallet.address,
            to: onionAuction.address,
            success: false,
        });
    });

    it('should handle mixed TON and USDT purchases', async () => {
        // Set USDT configuration
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'SetUSDTAddress',
                usdt_master: usdtMaster.address,
                usdt_wallet: usdtWallet.address
            }
        );

        // Start auction
        const startTime = BigInt(Math.floor(Date.now() / 1000));
        const endTime = startTime + 86400n;
        
        await onionAuction.send(
            deployer.getSender(),
            { value: toNano('0.1') },
            {
                $$type: 'StartAuction',
                start_time: startTime,
                end_time: endTime,
                soft_cap: toNano('500000'),
                hard_cap: toNano('2000000'),
                initial_price: toNano('0.1')
            }
        );

        // Make TON purchase
        await onionAuction.send(
            buyer.getSender(),
            { value: toNano('52') },
            {
                $$type: 'Purchase',
                amount: toNano('50'),
                currency: 0n // TON
            }
        );

        // Make USDT purchase
        await onionAuction.send(
            usdtWallet.getSender(),
            { value: toNano('0.2') },
            {
                $$type: 'JettonTransferNotification',
                query_id: 0n,
                amount: 50000000n, // 50 USDT
                sender: buyer.address,
                forward_payload: null
            }
        );

        // Check totals
        const totalRaisedTON = await onionAuction.getTotalRaised();
        const totalRaisedUSDT = await onionAuction.getTotalRaisedUsdt();
        const totalRaisedEquivalent = await onionAuction.getTotalRaisedEquivalent();

        expect(totalRaisedTON.toString()).toBe(toNano('50').toString());
        expect(totalRaisedUSDT.toString()).toBe('50000000');
        expect(totalRaisedEquivalent.toString()).toBe((toNano('50') + (50000000n * 1000n)).toString());
    });
});
