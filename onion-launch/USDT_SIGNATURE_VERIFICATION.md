# USDT 签名验证购买功能

## 🎯 概述

本文档描述了 OnionAuction 合约中新增的 USDT 签名验证购买功能。该功能允许用户通过 USDT 进行购买时使用 off-chain 计算和签名验证，提供更高的安全性和一致性。

## 🔧 技术实现

### 1. 合约层面改进

#### JettonTransferNotification 处理器增强
```tact
receive(msg: JettonTransferNotification) {
    // ... 基础验证 ...
    
    // 检查 forward_payload 是否包含签名验证数据
    if (msg.forward_payload != null) {
        // 处理带签名验证的 USDT 购买
        self.handleUSDTWithSignature(msg);
    } else {
        // 处理直接 USDT 购买（向后兼容）
        self.handleDirectUSDTPurchase(msg);
    }
}
```

#### 新增数据结构
```tact
// 解析后的购买数据结构
struct ParsedPurchaseData {
    calculation: PurchaseCalculation;
    signature: Slice;
}
```

#### 新增辅助函数
- `parsePurchaseFromPayload()` - 解析 forward_payload 中的购买计算和签名，返回 ParsedPurchaseData
- `handleUSDTWithSignature()` - 处理带签名验证的 USDT 购买
- `handleDirectUSDTPurchase()` - 处理直接 USDT 购买（向后兼容）

### 2. Forward Payload 数据结构

```tact
// forward_payload 包含以下数据：
Cell {
    user: Address,              // 用户地址
    amount: Int as coins,       // USDT 金额
    currency: Int as uint8,     // 货币类型 (1=USDT)
    tokens_to_receive: Int,     // 预计算的代币数量
    current_price: Int,         // 当前价格
    current_round: Int,         // 当前轮次
    timestamp: Int,             // 时间戳
    nonce: Int,                 // 防重放随机数
    signature: Cell             // 服务器签名 (存储在引用中)
}
```

## 🚀 使用方式

### 1. 前端集成

#### 安装依赖
```typescript
import { 
    USDTPurchaseService, 
    createUSDTPurchaseWithSignature,
    buildUSDTForwardPayload 
} from './lib/usdtPurchase'
```

#### 基本使用
```typescript
// 初始化服务
const usdtService = new USDTPurchaseService('https://your-api-server.com')

// 创建带签名的购买
const { calculation, signature, forwardPayload } = await createUSDTPurchaseWithSignature(
    userAddress,
    usdtAmount,
    usdtService
)

// 发送 USDT 转账
const usdtTransfer = {
    $$type: 'JettonTransfer',
    query_id: 0n,
    amount: usdtAmount,
    destination: auctionAddress,
    response_destination: userAddress,
    custom_payload: null,
    forward_ton_amount: toNano('0.05'), // 足够的 gas 用于签名验证
    forward_payload: forwardPayload
}
```

### 2. API 服务器实现

#### 签名端点
```typescript
POST /api/purchase/sign
Content-Type: application/json

{
    "user": "EQC...",
    "amount": "50000000",
    "currency": 1,
    "tokensToReceive": "500000000000",
    "currentPrice": "100000000",
    "currentRound": 1,
    "timestamp": 1640995200,
    "nonce": "123456789"
}

Response:
{
    "signature": "base64_encoded_signature"
}
```

#### 拍卖状态端点
```typescript
GET /api/auction/state

Response:
{
    "currentPrice": "100000000",
    "currentRound": 1,
    "tokensAvailable": "1000000000000"
}
```

## 🔒 安全特性

### 1. 签名验证
- 使用椭圆曲线数字签名算法 (ECDSA)
- 服务器私钥签名，合约公钥验证
- 防止价格操纵和计算错误

### 2. 防重放攻击
- 每个购买使用唯一的 nonce
- 合约记录已使用的 nonce
- 防止重复提交相同的购买请求

### 3. 时间窗口限制
- 签名有效期限制（默认 5 分钟）
- 防止过期签名的使用
- 确保价格和轮次的时效性

### 4. 数据一致性验证
- 验证 forward_payload 中的数据与实际转账匹配
- 确保用户地址、金额、货币类型的一致性
- 防止数据篡改攻击

## 📊 购买流程对比

### 传统直接购买
```
用户 → USDT转账 → 合约计算 → 处理购买
```

### 签名验证购买
```
用户 → API获取状态 → 本地计算 → API签名 → USDT转账+签名 → 合约验证 → 处理购买
```

## 🧪 测试

### 运行测试
```bash
# 运行 USDT 签名验证测试
npm test -- tests/OnionAuction.usdt.signature.spec.ts

# 运行所有 USDT 相关测试
npm test -- tests/OnionAuction.usdt*.spec.ts
```

### 测试覆盖
- ✅ 正常签名验证购买
- ✅ 无效签名拒绝
- ✅ 重放攻击防护
- ✅ 向后兼容性
- ✅ 时间戳验证
- ✅ 数据一致性检查

## 🔄 向后兼容性

新功能完全向后兼容：
- 空的 `forward_payload` 仍然触发直接购买
- 现有的 USDT 购买流程继续工作
- 不需要强制升级现有集成

## 📈 性能优化

### Gas 消耗
- 签名验证购买：~0.05 TON
- 直接购买：~0.02 TON
- 额外的 gas 用于签名验证和 payload 解析

### 存储优化
- 使用 map 存储已使用的 nonce
- 定期清理过期的 nonce（可选）
- 最小化状态存储开销

## 🛠️ 部署和配置

### 1. 合约配置
```typescript
// 设置签名公钥
await onionAuction.sendSetSigningKey(deployer.getSender(), {
    value: toNano('0.05'),
    publicKey: signingPublicKey
})

// 配置签名有效期（可选）
await onionAuction.sendSetSignatureTimeout(deployer.getSender(), {
    value: toNano('0.05'),
    timeout: 300 // 5 分钟
})
```

### 2. API 服务器配置
```typescript
// 生成密钥对
const keyPair = generateKeyPair()

// 配置签名服务
const signingService = new SigningService(keyPair.privateKey)

// 设置合约公钥
await onionAuction.setSigningKey(keyPair.publicKey)
```

## 🚨 注意事项

### 安全建议
1. **私钥安全**：确保 API 服务器私钥的安全存储
2. **HTTPS 通信**：API 通信必须使用 HTTPS
3. **速率限制**：对签名请求实施速率限制
4. **日志记录**：记录所有签名请求用于审计

### 最佳实践
1. **错误处理**：实现完善的错误处理和重试机制
2. **监控告警**：监控签名验证失败率
3. **备份恢复**：定期备份 nonce 状态
4. **版本管理**：维护 API 版本兼容性

## 📚 相关文档

- [USDT 集成指南](./USDT_INTEGRATION.md)
- [签名验证设计](./SIGNATURE_VERIFICATION_DESIGN.md)
- [API 文档](./API_DOCUMENTATION.md)
- [部署指南](./DEPLOYMENT_GUIDE.md)
