// Setup for handling BigInt serialization in Jest
// This prevents "Do not know how to serialize a BigInt" errors

// Override JSON.stringify to handle BigInt
(BigInt.prototype as any).toJSON = function() {
    return this.toString();
};

// Add custom matcher for BigInt comparison
expect.extend({
    toBeBigInt(received: any, expected: bigint) {
        const pass = typeof received === 'bigint' && received === expected;
        if (pass) {
            return {
                message: () => `expected ${received} not to be ${expected}`,
                pass: true,
            };
        } else {
            return {
                message: () => `expected ${received} to be ${expected}`,
                pass: false,
            };
        }
    },
});

