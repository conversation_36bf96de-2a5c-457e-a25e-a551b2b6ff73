import type { Config } from 'jest';

const config: Config = {
    preset: 'ts-jest',
    globalSetup: './jest.setup.ts',
    cache: false, // disabled caching to prevent old Tact files from being used after a rebuild
    testEnvironment: '@ton/sandbox/jest-environment',
    testPathIgnorePatterns: ['/node_modules/', '/dist/'],
    reporters: ['default', ['@ton/sandbox/jest-reporter', {}]],
    setupFilesAfterEnv: ['<rootDir>/jest.bigint-setup.ts'],
};

export default config;
