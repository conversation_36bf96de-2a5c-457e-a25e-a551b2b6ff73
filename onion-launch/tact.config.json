{"$schema": "https://raw.githubusercontent.com/tact-lang/tact/main/src/config/configSchema.json", "projects": [{"name": "OnionAuction", "path": "contracts/onion_auction.tact", "output": "build/OnionAuction", "options": {"debug": false, "external": false}, "mode": "full"}, {"name": "UserPurchase", "path": "contracts/user_purchase.tact", "output": "build/UserPurchase", "options": {"debug": false, "external": false}, "mode": "full"}]}